"""
Line discarding algorithms for diff preprocessing.

This module implements GNU diff-style line discarding heuristics to improve
diff quality by removing confusing or repetitive lines before running the
main diff algorithm.
"""

from typing import List
from diff_common import FileData, build_equivalence_classes


def discard_confusing_lines(filevec: List[FileData], discard: bool = True) -> None:
    """
    Mark lines to discard following GNU diff algorithm.
    Discarded lines are immediately marked as changed.
    
    Args:
        filevec: List of FileData objects for the two files being compared
        discard: If False, don't discard any lines (keep all lines for diff)
    """
    # Always build equivalence classes
    build_equivalence_classes(filevec)
    
    if not discard:
        # If discard=False, don't discard any lines, but still set up the arrays
        for f in range(2):
            end = filevec[f].buffered_lines
            filevec[f].realindexes = list(range(end))
            filevec[f].nondiscarded_lines = end
        return

    # Count occurrences of each equivalence class in each file
    equiv_count = [[0] * filevec[0].equiv_max for _ in range(2)]
    
    for f in range(2):
        equivs = filevec[f].equivs
        if equivs is not None:
            for i in range(filevec[f].buffered_lines):
                equiv_count[f][equivs[i]] += 1

    # Mark lines for discarding using GNU diff heuristics
    # Now using int lists: 0=keep, 1=discard, 2=provisional
    discarded = [[0] * filevec[f].buffered_lines for f in range(2)]
    
    for f in range(2):
        end = filevec[f].buffered_lines
        other_counts = equiv_count[1 - f]
        equivs = filevec[f].equivs
        
        # Calculate threshold like GNU diff
        many = 5
        tem = end // 64
        
        # Multiply MANY by approximate square root of number of lines
        while tem > 0:
            many *= 2
            tem >>= 2
            
        for i in range(end):
            if equivs is not None and equivs[i] == 0:  # Skip blank lines
                continue
                
            if equivs is not None:
                nmatch = other_counts[equivs[i]]
            else:
                nmatch = 0
            if nmatch == 0:
                # No matches in other file - definitely discard
                discarded[f][i] = 1
            elif nmatch > many:
                # Too many matches - provisionally mark for discarding
                discarded[f][i] = 2

    # Don't really discard the provisional lines except when they occur
    # in a run of discardables, with nonprovisionals at the beginning and end
    for f in range(2):
        end = filevec[f].buffered_lines
        discards = discarded[f]
        
        i = 0
        while i < end:
            # Cancel provisional discards not in middle of run of discards
            if discards[i] == 2:
                discards[i] = 0
            elif discards[i] != 0:
                # We have found a nonprovisional discard
                j = i
                length = 0
                provisional = 0
                
                # Find end of this run of discardable lines
                # Count how many are provisionally discardable
                while j < end and discards[j] != 0:
                    if discards[j] == 2:
                        provisional += 1
                    j += 1
                
                # Cancel provisional discards at end, and shrink the run
                while j > i and discards[j - 1] == 2:
                    discards[j - 1] = 0
                    provisional -= 1
                    j -= 1
                
                # Now we have the length of a run of discardable lines
                # whose first and last are not provisional
                length = j - i
                
                # If 1/4 of the lines in the run are provisional,
                # cancel discarding of all provisional lines in the run
                if provisional * 4 > length:
                    k = j
                    while k > i:
                        k -= 1
                        if discards[k] == 2:
                            discards[k] = 0
                else:
                    # Handle subruns of provisionals
                    consec = 0
                    minimum = 1
                    tem = length >> 2
                    
                    # MINIMUM is approximate square root of LENGTH/4
                    while tem > 0:
                        minimum <<= 1
                        tem >>= 2
                    minimum += 1
                    
                    # Cancel any subrun of MINIMUM or more provisionals within the larger run
                    for k in range(length):
                        if discards[i + k] != 2:
                            consec = 0
                        else:
                            consec += 1
                            if consec == minimum:
                                # Back up to start of subrun, to cancel it all
                                m = k - consec + 1
                                while m <= k:
                                    discards[i + m] = 0
                                    m += 1
                                consec = 0
                            elif consec > minimum:
                                discards[i + k] = 0
                    
                    # Scan from beginning of run until we find 3 or more nonprovisionals
                    # in a row or until the first nonprovisional at least 8 lines in.
                    # Until that point, cancel any provisionals.
                    for k in range(length):
                        if k >= 8 and discards[i + k] == 1:
                            break
                        if discards[i + k] == 2:
                            consec = 0
                            discards[i + k] = 0
                        elif discards[i + k] == 0:
                            consec = 0
                        else:
                            consec += 1
                            if consec == 3:
                                break
                    
                    # Same thing, from end
                    for k in range(length):
                        if k >= 8 and discards[i + length - k - 1] == 1:
                            break
                        if discards[i + length - k - 1] == 2:
                            consec = 0
                            discards[i + length - k - 1] = 0
                        elif discards[i + length - k - 1] == 0:
                            consec = 0
                        else:
                            consec += 1
                            if consec == 3:
                                break
                
                # Advance to end of this run
                i = j - 1
            
            i += 1
            
    # Actually discard the lines and mark them as changed
    for f in range(2):
        discards = discarded[f]
        end = filevec[f].buffered_lines
        j = 0
        kept_indices = []
        
        for i in range(end):
            if discards[i] == 0:  # Keep this line
                # Keep this line for main diff
                kept_indices.append(i)
                j += 1
            else:
                # Discard this line - mark as changed immediately
                filevec[f].changed[i] = True
                filevec[f].discard_mask[i] = discards[i]
        
        filevec[f].realindexes = kept_indices        
        filevec[f].nondiscarded_lines = j 