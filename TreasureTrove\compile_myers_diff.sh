#!/bin/bash

# Compilation script for Myers diff C++ library with comprehensive Diff class

echo "Compiling Myers diff C++ library with complete Diff class implementation..."

if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    # Linux or macOS
    echo "Compiling for Linux/macOS..."
    
    # Use C++11 standard for template features
    g++ -shared -fPIC -O3 -march=native -ffast-math -flto -DNDEBUG -std=c++11 -o myers_diff.so myers_diff.cpp
    
    if [ $? -eq 0 ]; then
        echo "Successfully compiled myers_diff.so with complete Diff class"
        
        # Optional: Create a simple test to verify the library loads
        echo "Verifying library can be loaded..."
        if command -v nm &> /dev/null; then
            echo "Exported symbols:"
            nm -D myers_diff.so | grep myers_ | head -5
        fi
        
        echo "Library size: $(ls -lh myers_diff.so | awk '{print $5}')"
    else
        echo "Compilation failed"
        exit 1
    fi
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # Windows
    echo "Compiling for Windows..."
    echo "Please use Visual Studio x64 Native Tools Command Prompt and run:"
    echo "cl /LD /O2 /std:c++11 /DMYERS_DIFF_EXPORTS myers_diff.cpp /Fe:myers_diff.dll /link /MACHINE:X64"
    echo ""
    echo "Alternative command for 32-bit:"
    echo "cl /LD /O2 /std:c++11 /DMYERS_DIFF_EXPORTS myers_diff.cpp /Fe:myers_diff.dll"
    echo ""
    echo "Note: The new implementation requires C++11 for template features"
else
    echo "Unknown OS type: $OSTYPE"
    exit 1
fi

echo "Compilation complete!"
echo ""
echo "The library now includes:"
echo "- Complete template-based Diff class"
echo "- Zero-copy algorithms using iterators"
echo "- Binary search-based prefix/suffix detection"
echo "- Full C API compatibility with new myers_diff_compute() function"
echo "- Explicit template instantiation for int type"
