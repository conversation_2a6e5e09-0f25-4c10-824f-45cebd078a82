#include "myers_diff.hpp"
#include <algorithm>
#include <vector>
#include <cstring>

// Template implementation for Diff class
template<typename T>
void Diff<T>::ensure_V(size_t len) const {
    if (V1_.size() < len) {
        V1_.assign(len, -1);
        V2_.assign(len, -1);
    } else {
        std::fill(V1_.begin(), V1_.end(), -1);
        std::fill(V2_.begin(), V2_.end(), -1);
    }
}

template<typename T>
size_t Diff<T>::common_prefix_len(const Slice<T>& A, const Slice<T>& B) {
    size_t n = std::min(A.size(), B.size());
    size_t i = 0;
    while (i < n && A[i] == B[i]) ++i;
    return i;
}

template<typename T>
size_t Diff<T>::common_suffix_len(const Slice<T>& A, const Slice<T>& B) {
    size_t n = std::min(A.size(), B.size());
    size_t i = 0;
    while (i < n && A[A.size() - 1 - i] == B[B.size() - 1 - i]) ++i;
    return i;
}

template<typename T>
DiffResult<T> Diff<T>::diff_main(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_main_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_main_impl(Slice<T> A, Slice<T> B) {
    // Check for equality (speedup)
    if (A.size() == B.size() &&
        std::equal(A.buf->begin() + A.begin,
                   A.buf->begin() + A.end,
                   B.buf->begin() + B.begin)) {
        if (!A.empty()) {
            return {{DIFF_EQUAL, {A.buf->begin() + A.begin,
                                  A.buf->begin() + A.end}}};
        }
        return {};
    }

    // Trim off common prefix (speedup)
    size_t prefix = common_prefix_len(A, B);
    std::vector<T> commonprefix;
    if (prefix > 0) {
        commonprefix.assign(A.buf->begin() + A.begin,
                           A.buf->begin() + A.begin + prefix);
    }
    
    Slice<T> A_trimmed{A.buf, A.begin + prefix, A.end};
    Slice<T> B_trimmed{B.buf, B.begin + prefix, B.end};

    // Trim off common suffix (speedup)
    size_t suffix = common_suffix_len(A_trimmed, B_trimmed);
    std::vector<T> commonsuffix;
    if (suffix > 0) {
        commonsuffix.assign(A_trimmed.buf->begin() + A_trimmed.end - suffix,
                           A_trimmed.buf->begin() + A_trimmed.end);
    }
    
    Slice<T> A_mid{A_trimmed.buf, A_trimmed.begin, A_trimmed.end - suffix};
    Slice<T> B_mid{B_trimmed.buf, B_trimmed.begin, B_trimmed.end - suffix};

    // Compute the diff on the middle block
    DiffResult<T> diffs = diff_compute_impl(A_mid, B_mid);

    // Restore the prefix and suffix
    if (!commonprefix.empty()) {
        diffs.insert(diffs.begin(), {DIFF_EQUAL, commonprefix});
    }
    if (!commonsuffix.empty()) {
        diffs.push_back({DIFF_EQUAL, commonsuffix});
    }

    return diffs;
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_compute_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute_impl(Slice<T> A, Slice<T> B) {
    if (A.empty()) {
        return {{DIFF_INSERT, {B.buf->begin() + B.begin, B.buf->begin() + B.end}}};
    }
    if (B.empty()) {
        return {{DIFF_DELETE, {A.buf->begin() + A.begin, A.buf->begin() + A.end}}};
    }

    // Use bisect algorithm
    return diff_bisect_impl(A, B);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_bisect_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
std::pair<size_t, size_t> Diff<T>::middle_snake(const std::vector<T>& text1, const std::vector<T>& text2) {
    Slice<T> A = make_slice(text1);
    Slice<T> B = make_slice(text2);
    
    // Cache the slice lengths
    size_t text1_length = A.size();
    size_t text2_length = B.size();
    size_t max_d = (text1_length + text2_length + 1) / 2;
    size_t v_offset = max_d;
    size_t v_length = 2 * max_d + 1 + 2; // +2 for guard cells
    
    // Ensure V buffer capacity
    ensure_V(v_length);
    
    // Optimize: precompute integer variables
    int text1_len = static_cast<int>(text1_length);
    int text2_len = static_cast<int>(text2_length);
    int max_d_i = static_cast<int>(max_d);
    int voff = static_cast<int>(v_offset);
    
    V1_[voff + 1] = 0;
    V2_[voff + 1] = 0;
    int delta = text1_len - text2_len;
    bool front = (delta % 2 != 0);
    
    int k1start = 0, k1end = 0, k2start = 0, k2end = 0;
    
    for (int d = 0; d < max_d_i; ++d) {
        // Walk the front path one step
        for (int k1 = -d + k1start; k1 < d + 1 - k1end; k1 += 2) {
            int k1_offset = voff + k1;
            int x1;
            // Choose predecessor
            if (k1 == -d || (k1 != d && V1_[k1_offset - 1] < V1_[k1_offset + 1])) {
                x1 = V1_[k1_offset + 1];
            } else {
                x1 = V1_[k1_offset - 1] + 1;
            }
            int y1 = x1 - k1;
            
            // Advance along the diagonal
            while (x1 < text1_len && y1 < text2_len && A[x1] == B[y1]) {
                ++x1; ++y1;
            }
            
            V1_[k1_offset] = x1;
            
            if (x1 > text1_len) {
                k1end += 2;
            } else if (y1 > text2_len) {
                k1start += 2;
            } else if (front) {
                int k2_offset = voff + delta - k1;
                if (k2_offset >= 0 && k2_offset < static_cast<int>(v_length) && V2_[k2_offset] != -1) {
                    int x2 = text1_len - V2_[k2_offset];
                    if (x1 >= x2) {
                        return {static_cast<size_t>(x1), static_cast<size_t>(y1)};
                    }
                }
            }
        }

        // Walk the reverse path one step
        for (int k2 = -d + k2start; k2 < d + 1 - k2end; k2 += 2) {
            int k2_offset = voff + k2;
            int x2;
            if (k2 == -d || (k2 != d && V2_[k2_offset - 1] < V2_[k2_offset + 1])) {
                x2 = V2_[k2_offset + 1];
            } else {
                x2 = V2_[k2_offset - 1] + 1;
            }
            int y2 = x2 - k2;
            
            while (x2 < text1_len && y2 < text2_len && A[text1_len - x2 - 1] == B[text2_len - y2 - 1]) {
                ++x2; ++y2;
            }
            
            V2_[k2_offset] = x2;
            
            if (x2 > text1_len) {
                k2end += 2;
            } else if (y2 > text2_len) {
                k2start += 2;
            } else if (!front) {
                int k1_off = voff + delta - k2;
                if (k1_off >= 0 && k1_off < static_cast<int>(v_length) && V1_[k1_off] != -1) {
                    int x1 = V1_[k1_off];
                    int y1 = voff + x1 - k1_off;
                    int x2_mirror = text1_len - x2;
                    if (x1 >= x2_mirror) {
                        return {static_cast<size_t>(x1), static_cast<size_t>(y1)};
                    }
                }
            }
        }
    }

    // No snake found within max_d iterations
    return {static_cast<size_t>(-1), static_cast<size_t>(-1)};
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect_impl(Slice<T> A, Slice<T> B) {
    // Check for equality (speedup)
    if (text1 == text2) {
        if (!text1.empty()) {
            return {{DIFF_EQUAL, text1}};
        }
        return {};
    }

    // Trim off common prefix (speedup)
    size_t commonlength = diff_commonPrefix(text1, text2);
    std::vector<T> commonprefix(text1.begin(), text1.begin() + commonlength);
    
    auto text1_trimmed_begin = text1.begin() + commonlength;
    auto text2_trimmed_begin = text2.begin() + commonlength;

    // Trim off common suffix (speedup)
    std::vector<T> text1_temp(text1_trimmed_begin, text1.end());
    std::vector<T> text2_temp(text2_trimmed_begin, text2.end());
    
    commonlength = diff_commonSuffix(text1_temp, text2_temp);
    std::vector<T> commonsuffix;
    if (commonlength > 0) {
        commonsuffix.assign(text1_temp.end() - commonlength, text1_temp.end());
        text1_temp.erase(text1_temp.end() - commonlength, text1_temp.end());
        text2_temp.erase(text2_temp.end() - commonlength, text2_temp.end());
    }

    // Compute the diff on the middle block
    DiffResult<T> diffs = diff_compute(text1_temp, text2_temp);

    // Restore the prefix and suffix
    if (!commonprefix.empty()) {
        diffs.insert(diffs.begin(), {DIFF_EQUAL, commonprefix});
    }
    if (!commonsuffix.empty()) {
        diffs.push_back({DIFF_EQUAL, commonsuffix});
    }

    return diffs;
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute(const std::vector<T>& text1, const std::vector<T>& text2) {
    if (text1.empty()) {
        return {{DIFF_INSERT, text2}};
    }
    if (text2.empty()) {
        return {{DIFF_DELETE, text1}};
    }

    // Determine longer and shorter sequences
    const std::vector<T>* longtext;
    const std::vector<T>* shorttext;
    bool text1_longer = text1.size() > text2.size();
    
    if (text1_longer) {
        longtext = &text1;
        shorttext = &text2;
    } else {
        shorttext = &text1;
        longtext = &text2;
    }

    // Use bisect algorithm
    return diff_bisect(text1, text2);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect(const std::vector<T>& text1, const std::vector<T>& text2) {
    // Cache the list lengths
    size_t text1_length = text1.size();
    size_t text2_length = text2.size();
    size_t max_d = (text1_length + text2_length + 1) / 2;
    size_t v_offset = max_d;
    size_t v_length = 2 * max_d + 1;
    
    // Optimize: precompute integer variables and pointers
    int text1_len = static_cast<int>(text1_length);
    int text2_len = static_cast<int>(text2_length);
    int max_d_i = static_cast<int>(max_d);
    int vlen = static_cast<int>(v_length);
    int voff = static_cast<int>(v_offset);
    const T* a = text1.data();
    const T* b = text2.data();
    
    // For very large inputs, v_length could be huge - add safety check
    
    std::vector<int> v1(vlen + 2, -1);

    std::vector<int> v2(vlen + 2, -1);
    v1[voff + 1] = 0;
    v2[voff + 1] = 0;
    int delta = text1_len - text2_len;
    bool front = (delta % 2 != 0);
    
    int k1start = 0, k1end = 0, k2start = 0, k2end = 0;
    
    for (int d = 0; d < max_d_i; ++d) {
        // Walk the front path one step
        for (int k1 = -d + k1start; k1 < d + 1 - k1end; k1 += 2) {
            int k1_offset = voff + k1;
            int x1;
            // Choose predecessor
            if (k1 == -d || (k1 != d && v1[k1_offset - 1] < v1[k1_offset + 1])) {
                x1 = v1[k1_offset + 1];
            } else {
                x1 = v1[k1_offset - 1] + 1;
            }
            int y1 = x1 - k1;
            
            // Advance along the diagonal using raw pointers
            while (x1 < text1_len && y1 < text2_len && a[x1] == b[y1]) {
                ++x1; ++y1;
            }
            
            v1[k1_offset] = x1;
            
            if (x1 > text1_len) {
                k1end += 2;
            } else if (y1 > text2_len) {
                k1start += 2;
            } else if (front) {
                int k2_offset = voff + delta - k1;
                if (k2_offset >= 0 && k2_offset < vlen && v2[k2_offset] != -1) {
                    int x2 = text1_len - v2[k2_offset];
                    if (x1 >= x2) {
                        return diff_bisectSplit(text1, text2, x1, y1);
                    }
                }
            }
        }

        // Walk the reverse path one step
        for (int k2 = -d + k2start; k2 < d + 1 - k2end; k2 += 2) {
            int k2_offset = voff + k2;
            int x2;
            if (k2 == -d || (k2 != d && v2[k2_offset - 1] < v2[k2_offset + 1])) {
                x2 = v2[k2_offset + 1];
            } else {
                x2 = v2[k2_offset - 1] + 1;
            }
            int y2 = x2 - k2;
            
            while (x2 < text1_len && y2 < text2_len && a[text1_len - x2 - 1] == b[text2_len - y2 - 1]) {
                ++x2; ++y2;
            }
            
            v2[k2_offset] = x2;
            
            if (x2 > text1_len) {
                k2end += 2;
            } else if (y2 > text2_len) {
                k2start += 2;
            } else if (!front) {
                int k1_off = voff + delta - k2;
                if (k1_off >= 0 && k1_off < vlen && v1[k1_off] != -1) {
                    int x1 = v1[k1_off];
                    int y1 = voff + x1 - k1_off;
                    int x2_mirror = text1_len - x2;
                    if (x1 >= x2_mirror) {
                        return diff_bisectSplit(text1, text2, x1, y1);
                    }
                }
            }
        }
    }

    // Diff took too long and hit the deadline or no commonality at all
    return {{DIFF_DELETE, text1}, {DIFF_INSERT, text2}};
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisectSplit(const std::vector<T>& text1, const std::vector<T>& text2, 
                                        size_t x, size_t y) {
    std::vector<T> text1a(text1.begin(), text1.begin() + x);
    std::vector<T> text2a(text2.begin(), text2.begin() + y);
    std::vector<T> text1b(text1.begin() + x, text1.end());
    std::vector<T> text2b(text2.begin() + y, text2.end());

    // Compute both diffs serially
    DiffResult<T> diffs = diff_main(text1a, text2a);
    DiffResult<T> diffsb = diff_main(text1b, text2b);

    // Concatenate results
    diffs.insert(diffs.end(), diffsb.begin(), diffsb.end());
    return diffs;
}

template<typename T>
size_t Diff<T>::diff_commonPrefix(const std::vector<T>& text1, const std::vector<T>& text2) {
    // Quick check for common null cases
    if (text1.empty() || text2.empty() || text1[0] != text2[0]) {
        return 0;
    }
    
    // Binary search for common prefix length
    size_t pointermin = 0;
    size_t pointermax = std::min(text1.size(), text2.size());
    size_t pointermid = pointermax;
    size_t pointerstart = 0;
    
    while (pointermin < pointermid) {
        // Compare ranges from pointerstart to pointermid
        bool match = std::equal(text1.begin() + pointerstart, text1.begin() + pointermid,
                               text2.begin() + pointerstart);
        
        if (match) {
            pointermin = pointermid;
            pointerstart = pointermin;
        } else {
            pointermax = pointermid;
        }
        pointermid = (pointermax + pointermin) / 2;
    }
    
    return pointermid;
}

template<typename T>
size_t Diff<T>::diff_commonSuffix(const std::vector<T>& text1, const std::vector<T>& text2) {
    // Quick check for common null cases
    if (text1.empty() || text2.empty() || text1.back() != text2.back()) {
        return 0;
    }
    
    // Binary search for common suffix length
    size_t pointermin = 0;
    size_t pointermax = std::min(text1.size(), text2.size());
    size_t pointermid = pointermax;
    size_t pointerend = 0;
    
    while (pointermin < pointermid) {
        // Compare suffix ranges
        bool match = std::equal(text1.end() - pointermid, text1.end() - pointerend,
                               text2.end() - pointermid);
        
        if (match) {
            pointermin = pointermid;
            pointerend = pointermin;
        } else {
            pointermax = pointermid;
        }
        pointermid = (pointermax + pointermin) / 2;
    }
    
    return pointermid;
}

template<typename T>
bool Diff<T>::list_endswith(const std::vector<T>& list1, const std::vector<T>& list2) {
    if (list2.empty() || list2.size() > list1.size()) {
        return false;
    }
    return std::equal(list2.begin(), list2.end(), list1.end() - list2.size());
}

template<typename T>
bool Diff<T>::list_startswith(const std::vector<T>& list1, const std::vector<T>& list2) {
    if (list2.empty() || list2.size() > list1.size()) {
        return false;
    }
    return std::equal(list2.begin(), list2.end(), list1.begin());
}

template<typename T>
int Diff<T>::find_subsequence(typename std::vector<T>::const_iterator longtext_begin,
                              typename std::vector<T>::const_iterator longtext_end,
                              typename std::vector<T>::const_iterator shorttext_begin,
                              typename std::vector<T>::const_iterator shorttext_end) {
    if (shorttext_begin == shorttext_end) {
        return 0;
    }
    
    size_t longtext_length = std::distance(longtext_begin, longtext_end);
    size_t shorttext_length = std::distance(shorttext_begin, shorttext_end);
    
    if (longtext_length < shorttext_length) {
        return -1;
    }
    
    for (size_t i = 0; i <= longtext_length - shorttext_length; ++i) {
        if (std::equal(shorttext_begin, shorttext_end, longtext_begin + i)) {
            return static_cast<int>(i);
        }
    }
    
    return -1;
}

// Range-based versions for zero-copy operations
template<typename T>
DiffResult<T> Diff<T>::diff_main_range(typename std::vector<T>::const_iterator text1_begin,
                                       typename std::vector<T>::const_iterator text1_end,
                                       typename std::vector<T>::const_iterator text2_begin,
                                       typename std::vector<T>::const_iterator text2_end) {
    // Convert to vectors for now - in practice, you might want to implement
    // fully range-based versions for maximum efficiency
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_main(text1_vec, text2_vec);
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute_range(typename std::vector<T>::const_iterator text1_begin,
                                          typename std::vector<T>::const_iterator text1_end,
                                          typename std::vector<T>::const_iterator text2_begin,
                                          typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_compute(text1_vec, text2_vec);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect_range(typename std::vector<T>::const_iterator text1_begin,
                                         typename std::vector<T>::const_iterator text1_end,
                                         typename std::vector<T>::const_iterator text2_begin,
                                         typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_bisect(text1_vec, text2_vec);
}

template<typename T>
size_t Diff<T>::diff_commonPrefix_range(typename std::vector<T>::const_iterator text1_begin,
                                        typename std::vector<T>::const_iterator text1_end,
                                        typename std::vector<T>::const_iterator text2_begin,
                                        typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_commonPrefix(text1_vec, text2_vec);
}

template<typename T>
size_t Diff<T>::diff_commonSuffix_range(typename std::vector<T>::const_iterator text1_begin,
                                        typename std::vector<T>::const_iterator text1_end,
                                        typename std::vector<T>::const_iterator text2_begin,
                                        typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_commonSuffix(text1_vec, text2_vec);
}

// Explicit template instantiation for int type
template class Diff<int>;

// C API compatibility layer - integrates with new Diff class
extern "C" {

int myers_diff_bisect(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int* x_out, int* y_out) 
{
    if (!text1 || !text2 || !x_out || !y_out) {
        return -1;
    }
    
    try {
        // Convert C arrays to vectors
        std::vector<int> text1_vec(text1, text1 + text1_length);
        std::vector<int> text2_vec(text2, text2 + text2_length);
        
        // Use the new Diff class
        Diff<int> diff;
        
        // For bisect-only operation, we need a specialized implementation
        // that just finds the middle snake without computing full diff
        DiffResult<int> result = diff.diff_bisect(text1_vec, text2_vec);
        
        // Parse result to extract coordinates
        // This is a simplified approach - the bisect method returns the full diff,
        // but we can still use the original implementation for the C API
        
        // Fall back to original implementation for precise x,y coordinate extraction
        // Cache the list lengths - use size_t to avoid overflow with large inputs
        size_t max_d = (static_cast<size_t>(text1_length) + static_cast<size_t>(text2_length) + 1) / 2;
        size_t v_offset = max_d;
        size_t v_length = 2 * max_d;
        
        // Check for potential memory issues with very large inputs
        if (v_length > 100000000) {  // Sanity check for ~400MB array
            *x_out = -1;
            *y_out = -1;
            return 0;
        }
        
        // Initialize arrays
        std::vector<int> v1(v_length, -1);
        v1[v_offset + 1] = 0;
        std::vector<int> v2 = v1;
        
        int delta = text1_length - text2_length;
        bool front = (delta % 2 != 0);
        
        // Offsets for start and end of k loop
        int k1start = 0;
        int k1end = 0;
        int k2start = 0;
        int k2end = 0;
        
        for (int d = 0; d < max_d; d++) {
            // Walk the front path one step
            for (int k1 = -d + k1start; k1 < d + 1 - k1end; k1 += 2) {
                int k1_offset = v_offset + k1;
                int x1;
                
                if (k1 == -d || (k1 != d && v1[k1_offset - 1] < v1[k1_offset + 1])) {
                    x1 = v1[k1_offset + 1];
                } else {
                    x1 = v1[k1_offset - 1] + 1;
                }
                int y1 = x1 - k1;
                
                while (x1 < text1_length && y1 < text2_length && text1[x1] == text2[y1]) {
                    x1++;
                    y1++;
                }
                
                v1[k1_offset] = x1;
                
                if (x1 > text1_length) {
                    k1end += 2;
                } else if (y1 > text2_length) {
                    k1start += 2;
                } else if (front) {
                    int k2_offset = v_offset + delta - k1;
                    if (k2_offset >= 0 && k2_offset < v_length && v2[k2_offset] != -1) {
                        int x2 = text1_length - v2[k2_offset];
                        if (x1 >= x2) {
                            *x_out = x1;
                            *y_out = y1;
                            return 0;
                        }
                    }
                }
            }
            
            // Walk the reverse path one step
            for (int k2 = -d + k2start; k2 < d + 1 - k2end; k2 += 2) {
                int k2_offset = v_offset + k2;
                int x2;
                
                if (k2 == -d || (k2 != d && v2[k2_offset - 1] < v2[k2_offset + 1])) {
                    x2 = v2[k2_offset + 1];
                } else {
                    x2 = v2[k2_offset - 1] + 1;
                }
                int y2 = x2 - k2;
                
                while (x2 < text1_length && y2 < text2_length && 
                       text1[text1_length - x2 - 1] == text2[text2_length - y2 - 1]) {
                    x2++;
                    y2++;
                }
                
                v2[k2_offset] = x2;
                
                if (x2 > text1_length) {
                    k2end += 2;
                } else if (y2 > text2_length) {
                    k2start += 2;
                } else if (!front) {
                    int k1_offset = v_offset + delta - k2;
                    if (k1_offset >= 0 && k1_offset < v_length && v1[k1_offset] != -1) {
                        int x1 = v1[k1_offset];
                        int y1 = v_offset + x1 - k1_offset;
                        x2 = text1_length - x2;
                        if (x1 >= x2) {
                            *x_out = x1;
                            *y_out = y1;
                            return 0;
                        }
                    }
                }
            }
        }
        
        // No snake found within max_d iterations
        *x_out = -1;
        *y_out = -1;
        return 0;
        
    } catch (const std::exception& e) {
        return -1;
    }
}

// New C API function for full diff computation
MYERS_API int myers_diff_compute(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int** operations_out,   // Output: array of operation types
    int*** data_out,        // Output: array of data arrays
    int** data_lengths_out, // Output: array of data lengths
    int* result_count_out   // Output: number of diff operations
) {
    if (!text1 || !text2 || !operations_out || !data_out || 
        !data_lengths_out || !result_count_out) {
        return -1;
    }
    
    try {
        // Check for negative lengths
        if (text1_length < 0 || text2_length < 0) {
            return -1;
        }
        
        // Check for extremely large inputs that might cause allocation failures
        const size_t MAX_SIZE = 100000000; // 100 million elements
        if (static_cast<size_t>(text1_length) > MAX_SIZE || 
            static_cast<size_t>(text2_length) > MAX_SIZE) {
            return -1;
        }
        
        // Convert C arrays to vectors
        std::vector<int> text1_vec;
        std::vector<int> text2_vec;
        
        // Reserve space to avoid reallocation
        text1_vec.reserve(text1_length);
        text2_vec.reserve(text2_length);
        
        // Copy data safely
        for (int i = 0; i < text1_length; ++i) {
            text1_vec.push_back(text1[i]);
        }
        for (int i = 0; i < text2_length; ++i) {
            text2_vec.push_back(text2[i]);
        }
        
        // Use the new Diff class
        Diff<int> diff;
        DiffResult<int> result = diff.diff_main(text1_vec, text2_vec);
        
        // Convert result back to C arrays
        *result_count_out = static_cast<int>(result.size());
        
        // Handle empty result case
        if (*result_count_out == 0) {
            *operations_out = nullptr;
            *data_out = nullptr;
            *data_lengths_out = nullptr;
            return 0;
        }
        
        // Allocate arrays for results
        *operations_out = static_cast<int*>(malloc(*result_count_out * sizeof(int)));
        *data_out = static_cast<int**>(malloc(*result_count_out * sizeof(int*)));
        *data_lengths_out = static_cast<int*>(malloc(*result_count_out * sizeof(int)));
        
        if (!*operations_out || !*data_out || !*data_lengths_out) {
            // Cleanup on allocation failure
            if (*operations_out) free(*operations_out);
            if (*data_out) free(*data_out);
            if (*data_lengths_out) free(*data_lengths_out);
            *operations_out = nullptr;
            *data_out = nullptr;
            *data_lengths_out = nullptr;
            return -1;
        }
        
        for (int i = 0; i < *result_count_out; ++i) {
            (*operations_out)[i] = result[i].first;
            (*data_lengths_out)[i] = static_cast<int>(result[i].second.size());
            
            // Handle empty data case
            if ((*data_lengths_out)[i] == 0) {
                (*data_out)[i] = nullptr;
                continue;
            }
            
            // Allocate data array for this operation
            (*data_out)[i] = static_cast<int*>(malloc((*data_lengths_out)[i] * sizeof(int)));
            if (!(*data_out)[i]) {
                // Cleanup on allocation failure
                for (int j = 0; j < i; ++j) {
                    if ((*data_out)[j]) free((*data_out)[j]);
                }
                free(*operations_out);
                free(*data_out);
                free(*data_lengths_out);
                *operations_out = nullptr;
                *data_out = nullptr;
                *data_lengths_out = nullptr;
                return -1;
            }
            
            // Copy data safely
            for (int j = 0; j < (*data_lengths_out)[i]; ++j) {
                if (j < static_cast<int>(result[i].second.size())) {
                    (*data_out)[i][j] = result[i].second[j];
                } else {
                    (*data_out)[i][j] = 0;  // Safety fallback
                }
            }
        }
        
        return 0;
        
    } catch (const std::bad_alloc& e) {
        // Memory allocation failure
        return -2;
    } catch (const std::exception& e) {
        // Other exceptions
        return -1;
    } catch (...) {
        // Unknown exception
        return -1;
    }
}

// Helper function to free memory allocated by myers_diff_compute
MYERS_API void myers_diff_free(
    int* operations,
    int** data,
    int* data_lengths,
    int result_count
) {
    if (data) {
        for (int i = 0; i < result_count; ++i) {
            if (data[i]) {
                free(data[i]);
            }
        }
        free(data);
    }
    if (operations) {
        free(operations);
    }
    if (data_lengths) {
        free(data_lengths);
    }
}

} // extern "C"