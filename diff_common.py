"""
Common components for diff algorithms.

This module provides shared data structures and utilities that can be reused
across different diff algorithm implementations.
"""

from typing import List, Tuple, Optional
import sys


class FileData:
    """
    Holds the lines of one file and parallel arrays indicating which lines
    we have 'discarded' for the main diff, as well as final alignment data.
    """
    __slots__ = ['lines', 'buffered_lines', 'discard_mask', 'undiscarded', 
                 'realindexes', 'nondiscarded_lines', 'changed', 'equivs', 'equiv_max']

    def __init__(self, lines: List[str]):
        self.lines: List[str] = lines
        self.buffered_lines: int = len(lines)

        # Mark lines that we want to skip from the main diff
        # Changed to int list to support provisional discards (0=keep, 1=discard, 2=provisional)
        self.discard_mask: List[int] = [0] * self.buffered_lines

        # Lines that should be included in the main diff algorithm
        self.undiscarded: Optional[List[int]] = None
        # Map from undiscarded indices back to original line indices  
        self.realindexes: Optional[List[int]] = None
        self.nondiscarded_lines: int = 0

        # Track which lines have been changed (deleted/inserted)
        # This includes both discarded lines and lines changed by the main algorithm
        self.changed: List[bool] = [False] * self.buffered_lines

        # For line equivalence classes (simplified version)
        self.equivs: Optional[List[int]] = None
        self.equiv_max: int = 0


def read_file_lines(filepath: str) -> List[str]:
    """Reads lines from a file, preserving original content."""
    try:
        with open(filepath, "r", encoding='utf-8') as f:
            lines = f.readlines()
        return lines
    except FileNotFoundError:
        print(f"Error: File not found {filepath}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        sys.exit(1)


def build_equivalence_classes(filevec: List[FileData]) -> None:
    """Build equivalence classes for lines across both files."""
    line_to_equiv = {}
    equiv_counter = 1  # 0 is reserved for blank/special lines
    
    # Assign equivalence classes to all lines
    for f in range(2):
        filevec[f].equivs = [0] * filevec[f].buffered_lines
        
        for i in range(filevec[f].buffered_lines):
            # Don't strip! GNU diff compares exact lines
            line = filevec[f].lines[i]
            
            # GNU diff only treats completely empty lines specially
            # A line with just spaces is NOT considered blank
            if filevec[f].equivs is not None:
                if len(line) == 0 or (len(line) == 1 and line[0] == '\n'):
                    # This is truly blank
                    filevec[f].equivs[i] = 0
                else:
                    if line not in line_to_equiv:
                        line_to_equiv[line] = equiv_counter
                        equiv_counter += 1
                    filevec[f].equivs[i] = line_to_equiv[line]
    
    # Set the maximum equivalence class number
    for f in range(2):
        filevec[f].equiv_max = equiv_counter


def build_undiscarded_arrays(filevec: List[FileData]) -> None:
    """
    Build arrays containing only undiscarded lines from each file.
    Creates the undiscarded array for the main diff algorithm.
    """
    for f in range(2):
        fdata = filevec[f]
        
        if fdata.realindexes is not None and len(fdata.realindexes) > 0:
            # Build undiscarded array using equivalence classes
            fdata.undiscarded = [0] * fdata.nondiscarded_lines
            for i in range(fdata.nondiscarded_lines):
                orig_idx = fdata.realindexes[i]
                fdata.undiscarded[i] = fdata.equivs[orig_idx]
        else:
            # No lines kept
            fdata.undiscarded = []
            fdata.realindexes = []


def build_diff_ops_from_changed_arrays(file_data: List[FileData]) -> List[Tuple[str, Optional[int], Optional[int], str]]:
    """
    Build diff operations from the changed arrays, following standard diff output format.
    
    Returns:
        List of tuples (op, a_line_num, b_line_num, text) where:
        - op is '+', '-', or 'equal'
        - line numbers are 1-based
        - text is the actual line content
    """

    final_ops = []
    changed0 = file_data[0].changed
    changed1 = file_data[1].changed
    len0 = file_data[0].buffered_lines
    len1 = file_data[1].buffered_lines
    
    i0 = 0
    i1 = 0
    while i0 < len0 or i1 < len1:
        if (i0 < len0 and changed0[i0]) or (i1 < len1 and changed1[i1]):
            # Handle changed region
            start0, start1 = i0, i1
            
            # Find extent of changes
            while i0 < len0 and changed0[i0]:
                i0 += 1
            while i1 < len1 and changed1[i1]:
                i1 += 1
                
            # Output deletions
            for idx in range(start0, i0):
                final_ops.append(("-", idx + 1, None, file_data[0].lines[idx]))
                
            # Output insertions  
            for idx in range(start1, i1):
                final_ops.append(("+", None, idx + 1, file_data[1].lines[idx]))
        else:
            # Equal lines
            if i0 < len0 and i1 < len1:
                final_ops.append(("equal", i0 + 1, i1 + 1, file_data[0].lines[i0]))
            i0 += 1
            i1 += 1
            
    return final_ops


def shift_boundaries(file_data: List[FileData]) -> None:
    """
    Shift change boundaries to make cleaner diffs, following GNU diff algorithm.
    This implementation properly tracks positions in both files.
    """
    for f in range(2):
        changed = file_data[f].changed
        other_changed = file_data[1 - f].changed
        equivs = file_data[f].equivs
        i = 0
        j = 0  # Track position in other file
        i_end = file_data[f].buffered_lines
        other_i_end = file_data[1 - f].buffered_lines
        
        while True:
            # Find beginning of next run of changes
            # Also keep track of the corresponding point in the other file
            while i < i_end and not changed[i]:
                while j < other_i_end and other_changed[j]:
                    j += 1
                i += 1
                if j < other_i_end:
                    j += 1
                
            if i == i_end:
                break
                
            start = i
            
            # Find the end of this run of changes
            while i < i_end and changed[i]:
                i += 1
            while j < other_i_end and other_changed[j]:
                j += 1
                
            # Loop until no more changes - ensures convergence like GNU diff
            while True:
                # Record the length of this run of changes
                runlength = i - start
                
                # Move the changed region back, so long as the
                # previous unchanged line matches the last changed one.
                # This merges with previous changed regions.
                while (start > 0 and i > 0 and 
                       equivs[start - 1] == equivs[i - 1]):
                    changed[start - 1] = True
                    changed[i - 1] = False
                    start -= 1
                    i -= 1
                    
                    # Skip preceding changed lines in both files
                    while start > 0 and changed[start - 1]:
                        start -= 1
                    
                    # Update j to stay synchronized
                    if j > 0:
                        j -= 1
                        while j > 0 and other_changed[j - 1]:
                            j -= 1
                
                # Set CORRESPONDING to the end of the changed run, at the last
                # point where it corresponds to a changed run in the other file.
                # CORRESPONDING == I_END means no such point has been found.
                corresponding = i
                if j > 0 and other_changed[j - 1]:
                    corresponding = i
                else:
                    corresponding = i_end
                
                # Move the changed region forward, so long as the
                # first changed line matches the following unchanged one.
                # This merges with following changed regions.
                # Do this second, so that if there are no merges,
                # the changed region is moved forward as far as possible.
                while i < i_end and start < i and equivs[start] == equivs[i]:
                    changed[start] = False
                    changed[i] = True
                    start += 1
                    i += 1
                    
                    # Skip following changed lines
                    while i < i_end and changed[i]:
                        i += 1
                        
                    # Keep j synchronized
                    if j < other_i_end:
                        j += 1
                        while j < other_i_end and other_changed[j]:
                            corresponding = i
                            j += 1
                
                # If no changes occurred, we've converged
                if runlength == i - start:
                    break
            
            # If possible, move the fully-merged run of changes
            # back to a corresponding run in the other file.
            while corresponding < i:
                changed[start - 1] = True
                changed[i - 1] = False
                start -= 1
                i -= 1
                
                # Keep j synchronized
                if j > 0:
                    j -= 1
                    while j > 0 and other_changed[j - 1]:
                        j -= 1 