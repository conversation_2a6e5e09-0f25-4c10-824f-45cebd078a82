#ifndef MYERS_DIFF_HPP
#define MYERS_DIFF_HPP

#include <vector>
#include <utility>
#include <iterator>
#include <algorithm>
#include <cstdint>

#ifdef _WIN32
    #ifdef MYERS_DIFF_EXPORTS
        #define MYERS_API __declspec(dllexport)
    #else
        #define MYERS_API __declspec(dllimport)
    #endif
#else
    #define MYERS_API
#endif

// Diff operation constants
const int DIFF_DELETE = -1;
const int DIFF_INSERT = 1;
const int DIFF_EQUAL = 0;

// Diff result type: vector of (operation, data) pairs
template<typename T>
using DiffResult = std::vector<std::pair<int, std::vector<T>>>;

// Lightweight "view" struct for zero-copy operations

template<typename T>
struct Slice {
    const T* data;
    int32_t len;
    
    Slice(const T* d, int32_t l) : data(d), len(l) {}

    int32_t size() const { return len; }
    bool empty() const { return len == 0; }
    const T& operator[](int32_t i) const { return data[i]; }
    // Helper to create a sub-slice
    Slice sub_slice(int32_t pos, int32_t new_len) const {
        return {data + pos, new_len};
    }
};

// Helper to build a Slice from a whole vector
template<typename T>
Slice<T> make_slice(const std::vector<T>& v) {
    return {v.data(), static_cast<int32_t>(v.size())};
}

// Helper to build a Slice from a C-style array
template<typename T>
Slice<T> make_slice(const T* data, int32_t len) {
    return {data, len};
}



// template<typename T>
// struct Slice {
//     const T* data;
//     int32_t begin;
//     int32_t end;          // one-past-the-last

//     int32_t size()  const { return end - begin; }
//     bool   empty() const { return begin == end; }
//     const T& operator[](int32_t i) const { return data[begin + i]; }
// };

// // Helper to build a Slice from a whole vector
// template<typename T>
// Slice<T> make_slice(const std::vector<T>& v)
// { return {v.data(), 0, static_cast<int32_t>(v.size())}; }

// Adaptive index buffer templates for memory optimization
template<typename IndexT>
using Vec = std::vector<IndexT>;

template<typename T>
class Diff {
public:
    // Configuration parameters
    int LCS_MinLength = 4;      // Minimum length to consider LCS optimization
    double LCS_MinRatio = 0.3;  // Minimum ratio of LCS to longer text (30%)

    // Constructor
    Diff() = default;

    // Helper that returns middle snake coordinates for C API
    std::pair<int32_t, int32_t> middle_snake(const std::vector<T>& text1, const std::vector<T>& text2);
    
    // Zero-copy version that works with Slice objects
    std::pair<int32_t, int32_t> middle_snake_slice(const Slice<T>& slice1, const Slice<T>& slice2);

private:
    // Reusable scratch buffers for V arrays
    std::vector<int32_t> V1_;
    std::vector<int32_t> V2_;

    // Core Myers algorithm implementation with adaptive index types
    template<typename IndexT>
    std::pair<int32_t, int32_t> middle_snake_core(IndexT* Vf, IndexT* Vr,
                                                  int32_t v_len, int32_t N, int32_t M,
                                                  const T* a, const T* b);
};

// C API compatibility layer
extern "C" {
    // Main diff_bisect function
    // Returns 0 on success, -1 on failure
    // Results are returned via x_out and y_out parameters
    MYERS_API int myers_diff_bisect(
        const int* text1,      // First sequence
        int text1_length,      // Length of first sequence
        const int* text2,      // Second sequence  
        int text2_length,      // Length of second sequence
        int* x_out,           // Output: x coordinate of middle snake
        int* y_out            // Output: y coordinate of middle snake
    );
}

#endif // MYERS_DIFF_HPP