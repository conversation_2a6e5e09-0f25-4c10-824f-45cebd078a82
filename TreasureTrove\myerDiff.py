"""
Myers diff algorithm implementation.

This module provides a unified implementation of the Myers diff algorithm
for finding the shortest edit script between two sequences, incorporating
both the core algorithm and supporting data structures.
"""

from typing import List, Tuple, Optional
import time
import sys 

# Import common components
from diff_common import FileData, build_undiscarded_arrays, build_diff_ops_from_changed_arrays, shift_boundaries, read_file_lines
from line_discarding import discard_confusing_lines
from diff_formatting import format_diff_ops_to_blocks, apply_formatted_diff
from html_diff_visualizer import generate_html_diff_visualization
from myers_numba_core import diff_bisect_optimized
import os
import ctypes
import numpy as np
import platform

# Load the C++ shared library
total_meyer_cpp = 0.0
try:
    if platform.system() == 'Windows':
        # Try multiple possible locations for the DLL
        dll_paths = ['./myers_diff.dll', 'myers_diff.dll', 
                     os.path.join(os.path.dirname(__file__), 'myers_diff.dll')]
        myers_lib = None
        for dll_path in dll_paths:
            try:
                myers_lib = ctypes.CDLL(dll_path)
                print(f"Successfully loaded C++ library from: {dll_path}")
                break
            except OSError as e:
                continue
        if myers_lib is None:
            raise OSError("Could not find myers_diff.dll in any expected location")
    else:
        # Linux/Unix
        so_paths = ['./myers_diff.so', 'myers_diff.so',
                    os.path.join(os.path.dirname(__file__), 'myers_diff.so')]
        myers_lib = None
        for so_path in so_paths:
            try:
                myers_lib = ctypes.CDLL(so_path)
                print(f"Successfully loaded C++ library from: {so_path}")
                break
            except OSError as e:
                continue
        if myers_lib is None:
            raise OSError("Could not find myers_diff.so in any expected location")
    
    # Define function signatures
    myers_lib.myers_diff_bisect.argtypes = [
        ctypes.POINTER(ctypes.c_int),  # text1
        ctypes.c_int,                   # text1_length
        ctypes.POINTER(ctypes.c_int),  # text2
        ctypes.c_int,                   # text2_length
        ctypes.POINTER(ctypes.c_int),  # x_out
        ctypes.POINTER(ctypes.c_int)   # y_out
    ]
    myers_lib.myers_diff_bisect.restype = ctypes.c_int
    
    # Define new full diff computation function
    myers_lib.myers_diff_compute.argtypes = [
        ctypes.POINTER(ctypes.c_int),    # text1
        ctypes.c_int,                    # text1_length  
        ctypes.POINTER(ctypes.c_int),    # text2
        ctypes.c_int,                    # text2_length
        ctypes.POINTER(ctypes.POINTER(ctypes.c_int)),    # operations_out
        ctypes.POINTER(ctypes.POINTER(ctypes.POINTER(ctypes.c_int))),  # data_out
        ctypes.POINTER(ctypes.POINTER(ctypes.c_int)),    # data_lengths_out
        ctypes.POINTER(ctypes.c_int)     # result_count_out
    ]
    myers_lib.myers_diff_compute.restype = ctypes.c_int

    myers_lib.myers_diff_free.argtypes = [
        ctypes.POINTER(ctypes.c_int),    # operations
        ctypes.POINTER(ctypes.POINTER(ctypes.c_int)),  # data
        ctypes.POINTER(ctypes.c_int),    # data_lengths
        ctypes.c_int                     # result_count
    ]
    myers_lib.myers_diff_free.restype = None
    cpp_lib_loaded = True
except Exception as e:
    cpp_lib_loaded = False
    print(f"Warning: C++ Myers diff library not found. Error: {e}")
    print("Using Python implementation.")


class DiffMatchPatch:
    """Class containing the diff, match and patch methods."""

    def __init__(self):
        """Inits a diff_match_patch object with default settings."""
        # LCS optimization settings
        self.LCS_MinLength = 4  # Minimum length to consider LCS optimization
        self.LCS_MinRatio = 0.3  # Minimum ratio of LCS to longer text (30%)

    #  DIFF FUNCTIONS

    # The data structure representing a diff is an array of tuples:
    # [(DIFF_DELETE, "Hello"), (DIFF_INSERT, "Goodbye"), (DIFF_EQUAL, " world.")]
    # which means: delete "Hello", add "Goodbye" and keep " world."
    DIFF_DELETE = -1
    DIFF_INSERT = 1
    DIFF_EQUAL = 0

    def diff_main(self, text1, text2):
        """Find the differences between two lists."""
        # Check for null inputs.
        if text1 is None or text2 is None:
            raise ValueError("Null inputs. (diff_main)")

        # Check for equality (speedup).
        if text1 == text2:
            if text1:
                return [(self.DIFF_EQUAL, text1)]
            return []

        # Trim off common prefix (speedup).
        commonlength = self.diff_commonPrefix(text1, text2)
        commonprefix = text1[:commonlength]
        text1 = text1[commonlength:]
        text2 = text2[commonlength:]

        # Trim off common suffix (speedup).
        commonlength = self.diff_commonSuffix(text1, text2)
        if commonlength == 0:
            commonsuffix = []
        else:
            commonsuffix = text1[-commonlength:]
            text1 = text1[:-commonlength]
            text2 = text2[:-commonlength]

        # Compute the diff on the middle block.
        diffs = self.diff_compute(text1, text2)

        # Restore the prefix and suffix.
        if commonprefix:
            diffs[:0] = [(self.DIFF_EQUAL, commonprefix)]
        if commonsuffix:
            diffs.append((self.DIFF_EQUAL, commonsuffix))
        
        self.diff_cleanupMerge(diffs)
        return diffs 

    def diff_compute(self, text1, text2):
        """Find the differences between two lists. Assumes that the lists do not
          have any common prefix or suffix.
        """
        if not text1:
            # Just add some text (speedup).
            return [(self.DIFF_INSERT, text2)]

        if not text2:
            # Just delete some text (speedup).
            return [(self.DIFF_DELETE, text1)]

        if len(text1) > len(text2):
            (longtext, shorttext) = (text1, text2)
        else:
            (shorttext, longtext) = (text1, text2)
            
        # Check if shorttext is fully contained in longtext
        # This is a list-specific implementation of the string.find() logic
        
        for i in range(len(longtext) - len(shorttext) + 1):
            if all(longtext[i + j] == shorttext[j] for j in range(len(shorttext))):
                # Shorter list is inside the longer list (speedup).
                diffs = [(self.DIFF_INSERT, longtext[:i]), 
                         (self.DIFF_EQUAL, shorttext),
                         (self.DIFF_INSERT, longtext[i + len(shorttext):])]
                # Swap insertions for deletions if diff is reversed.
                if len(text1) > len(text2):
                    diffs[0] = (self.DIFF_DELETE, diffs[0][1]) if isinstance(diffs[0], tuple) and len(diffs[0]) > 1 else diffs[0]
                    diffs[2] = (self.DIFF_DELETE, diffs[2][1]) if isinstance(diffs[2], tuple) and len(diffs[2]) > 1 else diffs[2]
                return diffs
                
        if len(shorttext) == 1:
            # Single element list.
            # After the previous speedup, the element can't be an equality.
            return [(self.DIFF_DELETE, text1), (self.DIFF_INSERT, text2)]

        #return diff_bisect_optimized(self, text1, text2)
        # Use C++ implementation if available, otherwise fallback to Python
        return diff_bisect_cpp(self, text1, text2)

    def diff_bisect(self, text1, text2):
        """Find the 'middle snake' of a diff, split the problem in two
          and return the recursively constructed diff.
          See Myers 1986 paper: An O(ND) Difference Algorithm and Its Variations.
        """
        # Cache the list lengths to prevent multiple calls.
        text1_length = len(text1)
        text2_length = len(text2)
        max_d = (text1_length + text2_length + 1) // 2
        v_offset = max_d
        v_length = 2 * max_d
        v1 = [-1] * v_length
        v1[v_offset + 1] = 0
        v2 = v1[:]
        delta = text1_length - text2_length
        # If the total number of elements is odd, then the front path will
        # collide with the reverse path.
        front = (delta % 2 != 0)
        # Offsets for start and end of k loop.
        # Prevents mapping of space beyond the grid.
        k1start = 0
        k1end = 0
        k2start = 0
        k2end = 0
        for d in range(max_d):
            # Walk the front path one step.
            for k1 in range(-d + k1start, d + 1 - k1end, 2):
                k1_offset = v_offset + k1
                if k1 == -d or (k1 != d and
                                v1[k1_offset - 1] < v1[k1_offset + 1]):
                    x1 = v1[k1_offset + 1]
                else:
                    x1 = v1[k1_offset - 1] + 1
                y1 = x1 - k1
                while (x1 < text1_length and y1 < text2_length and
                       text1[x1] == text2[y1]):
                    x1 += 1
                    y1 += 1
                v1[k1_offset] = x1
                if x1 > text1_length:
                    # Ran off the right of the graph.
                    k1end += 2
                elif y1 > text2_length:
                    # Ran off the bottom of the graph.
                    k1start += 2
                elif front:
                    k2_offset = v_offset + delta - k1
                    if k2_offset >= 0 and k2_offset < v_length and v2[k2_offset] != -1:
                        # Mirror x2 onto top-left coordinate system.
                        x2 = text1_length - v2[k2_offset]
                        if x1 >= x2:
                            # Overlap detected.
                            return self.diff_bisectSplit(text1, text2, x1, y1)

            # Walk the reverse path one step.
            for k2 in range(-d + k2start, d + 1 - k2end, 2):
                k2_offset = v_offset + k2
                if k2 == -d or (k2 != d and
                                v2[k2_offset - 1] < v2[k2_offset + 1]):
                    x2 = v2[k2_offset + 1]
                else:
                    x2 = v2[k2_offset - 1] + 1
                y2 = x2 - k2
                while (x2 < text1_length and y2 < text2_length and
                       text1[text1_length - x2 - 1] == text2[text2_length - y2 - 1]):
                    x2 += 1
                    y2 += 1
                v2[k2_offset] = x2
                if x2 > text1_length:
                    # Ran off the left of the graph.
                    k2end += 2
                elif y2 > text2_length:
                    # Ran off the top of the graph.
                    k2start += 2
                elif not front:
                    k1_offset = v_offset + delta - k2
                    if k1_offset >= 0 and k1_offset < v_length and v1[k1_offset] != -1:
                        x1 = v1[k1_offset]
                        y1 = v_offset + x1 - k1_offset
                        # Mirror x2 onto top-left coordinate system.
                        x2 = text1_length - x2
                        if x1 >= x2:
                            # Overlap detected.
                            return self.diff_bisectSplit(text1, text2, x1, y1)

        # Diff took too long and hit the deadline or
        # number of diffs equals number of elements, no commonality at all.
        return [(self.DIFF_DELETE, text1), (self.DIFF_INSERT, text2)]

    def diff_bisectSplit(self, text1, text2, x, y):
        """Given the location of the 'middle snake', split the diff in two parts
        and recurse.
        """
        text1a = text1[:x]
        text2a = text2[:y]
        text1b = text1[x:]
        text2b = text2[y:]

        # Compute both diffs serially.
        diffs = self.diff_main(text1a, text2a)
        diffsb = self.diff_main(text1b, text2b)

        return diffs + diffsb
    
    def _diff_bisectSplit_indexed(self, text1, text2, x, y, start1, end1, start2, end2):
        """Optimized split that uses indices to avoid memory copies."""
        # Calculate split points in the current range
        split1 = start1 + x
        split2 = start2 + y
        
        # Recurse on left part (from start to split point)
        diffs = self._diff_main_indexed(text1, text2, start1, split1, start2, split2)
        
        # Recurse on right part (from split point to end)
        diffsb = self._diff_main_indexed(text1, text2, split1, end1, split2, end2)
        
        return diffs + diffsb

    def diff_commonPrefix(self, text1, text2):
        """Determine the common prefix of two lists using binary search optimization."""
        # Quick check for common null cases.
        if not text1 or not text2 or text1[0] != text2[0]:
            return 0
        
        # Binary search for common prefix length
        pointermin = 0
        pointermax = min(len(text1), len(text2))
        pointermid = pointermax
        pointerstart = 0
        
        while pointermin < pointermid:
            # Compare slices from pointerstart to pointermid
            if text1[pointerstart:pointermid] == text2[pointerstart:pointermid]:
                pointermin = pointermid
                pointerstart = pointermin
            else:
                pointermax = pointermid
            pointermid = (pointermax + pointermin) // 2
        
        return pointermid

    def diff_commonSuffix(self, text1, text2):
        """Determine the common suffix of two lists using binary search optimization."""
        # Quick check for common null cases.
        if not text1 or not text2 or text1[-1] != text2[-1]:
            return 0
        
        # Binary search for common suffix length
        pointermin = 0
        pointermax = min(len(text1), len(text2))
        pointermid = pointermax
        pointerend = 0
        
        while pointermin < pointermid:
            # Compare suffix slices
            if (text1[-pointermid:len(text1) - pointerend] == 
                text2[-pointermid:len(text2) - pointerend]):
                pointermin = pointermid
                pointerend = pointermin
            else:
                pointermax = pointermid
            pointermid = (pointermax + pointermin) // 2
        
        return pointermid

    def _diff_main_indexed(self, text1, text2, start1, end1, start2, end2):
        """Index-based version of diff_main to avoid memory copies."""
        # Check for null inputs
        if start1 >= end1 and start2 >= end2:
            return []
        if start1 >= end1:
            return [(self.DIFF_INSERT, text2[start2:end2])]
        if start2 >= end2:
            return [(self.DIFF_DELETE, text1[start1:end1])]
        
        # Check for equality (speedup)
        len1, len2 = end1 - start1, end2 - start2
        if len1 == len2:
            if all(text1[start1 + i] == text2[start2 + i] for i in range(len1)):
                return [(self.DIFF_EQUAL, text1[start1:end1])]
        
        # Trim off common prefix (speedup)
        commonlength = self._diff_commonPrefix_indexed(text1, text2, start1, end1, start2, end2)
        commonprefix = text1[start1:start1 + commonlength] if commonlength > 0 else []
        start1 += commonlength
        start2 += commonlength
        
        # Trim off common suffix (speedup)
        commonlength = self._diff_commonSuffix_indexed(text1, text2, start1, end1, start2, end2)
        if commonlength == 0:
            commonsuffix = []
        else:
            commonsuffix = text1[end1 - commonlength:end1]
            end1 -= commonlength
            end2 -= commonlength
        
        # Compute the diff on the middle block
        diffs = self._diff_compute_indexed(text1, text2, start1, end1, start2, end2)
        
        # Restore the prefix and suffix
        if commonprefix:
            diffs[:0] = [(self.DIFF_EQUAL, commonprefix)]
        if commonsuffix:
            diffs.append((self.DIFF_EQUAL, commonsuffix))
        
        self.diff_cleanupMerge(diffs)
        return diffs
    
    def _diff_compute_indexed(self, text1, text2, start1, end1, start2, end2):
        """Index-based version of diff_compute."""
        len1, len2 = end1 - start1, end2 - start2
        
        if len1 == 0:
            return [(self.DIFF_INSERT, text2[start2:end2])]
        if len2 == 0:
            return [(self.DIFF_DELETE, text1[start1:end1])]
        
        # Determine longer and shorter sequences
        if len1 > len2:
            long_start, long_end = start1, end1
            short_start, short_end = start2, end2
            longtext, shorttext = text1, text2
            is_text1_longer = True
        else:
            long_start, long_end = start2, end2
            short_start, short_end = start1, end1
            longtext, shorttext = text2, text1
            is_text1_longer = False
        
        long_len = long_end - long_start
        short_len = short_end - short_start
        
        # Check if shorter sequence is contained in longer sequence
        for i in range(long_len - short_len + 1):
            if all(longtext[long_start + i + j] == shorttext[short_start + j] for j in range(short_len)):
                # Found containment
                prefix = longtext[long_start:long_start + i]
                equal = shorttext[short_start:short_end]
                suffix = longtext[long_start + i + short_len:long_end]
                
                diffs = []
                if prefix:
                    diffs.append((self.DIFF_INSERT, prefix))
                diffs.append((self.DIFF_EQUAL, equal))
                if suffix:
                    diffs.append((self.DIFF_INSERT, suffix))
                
                # Swap insertions for deletions if diff is reversed
                if is_text1_longer:
                    for j in range(len(diffs)):
                        if diffs[j][0] == self.DIFF_INSERT and j != len(diffs) - 1 or (j == 0 and len(diffs) > 1):
                            diffs[j] = (self.DIFF_DELETE, diffs[j][1])
                
                return diffs
        
        if short_len == 1:
            # Single element case
            return [(self.DIFF_DELETE, text1[start1:end1]), (self.DIFF_INSERT, text2[start2:end2])]
        
        # Use bisect algorithm
        return self._diff_bisect_indexed(text1, text2, start1, end1, start2, end2)
    
    def _diff_bisect_indexed(self, text1, text2, start1, end1, start2, end2):
        """Index-based version of diff_bisect."""
        text1_length = end1 - start1
        text2_length = end2 - start2
        max_d = (text1_length + text2_length + 1) // 2
        v_offset = max_d
        v_length = 2 * max_d
        v1 = [-1] * v_length
        v1[v_offset + 1] = 0
        v2 = v1[:]
        delta = text1_length - text2_length
        front = (delta % 2 != 0)
        k1start = k1end = k2start = k2end = 0
        
        for d in range(max_d):
            # Walk the front path one step
            for k1 in range(-d + k1start, d + 1 - k1end, 2):
                k1_offset = v_offset + k1
                if k1 == -d or (k1 != d and v1[k1_offset - 1] < v1[k1_offset + 1]):
                    x1 = v1[k1_offset + 1]
                else:
                    x1 = v1[k1_offset - 1] + 1
                y1 = x1 - k1
                
                while (x1 < text1_length and y1 < text2_length and
                       text1[start1 + x1] == text2[start2 + y1]):
                    x1 += 1
                    y1 += 1
                
                v1[k1_offset] = x1
                if x1 > text1_length:
                    k1end += 2
                elif y1 > text2_length:
                    k1start += 2
                elif front:
                    k2_offset = v_offset + delta - k1
                    if k2_offset >= 0 and k2_offset < v_length and v2[k2_offset] != -1:
                        x2 = text1_length - v2[k2_offset]
                        if x1 >= x2:
                            return self._diff_bisectSplit_indexed(text1, text2, x1, y1, start1, end1, start2, end2)
            
            # Walk the reverse path one step
            for k2 in range(-d + k2start, d + 1 - k2end, 2):
                k2_offset = v_offset + k2
                if k2 == -d or (k2 != d and v2[k2_offset - 1] < v2[k2_offset + 1]):
                    x2 = v2[k2_offset + 1]
                else:
                    x2 = v2[k2_offset - 1] + 1
                y2 = x2 - k2
                
                while (x2 < text1_length and y2 < text2_length and
                       text1[start1 + text1_length - x2 - 1] == text2[start2 + text2_length - y2 - 1]):
                    x2 += 1
                    y2 += 1
                
                v2[k2_offset] = x2
                if x2 > text1_length:
                    k2end += 2
                elif y2 > text2_length:
                    k2start += 2
                elif not front:
                    k1_offset = v_offset + delta - k2
                    if k1_offset >= 0 and k1_offset < v_length and v1[k1_offset] != -1:
                        x1 = v1[k1_offset]
                        y1 = v_offset + x1 - k1_offset
                        x2 = text1_length - x2
                        if x1 >= x2:
                            return self._diff_bisectSplit_indexed(text1, text2, x1, y1, start1, end1, start2, end2)
        
        # No overlap found
        return [(self.DIFF_DELETE, text1[start1:end1]), (self.DIFF_INSERT, text2[start2:end2])]
    
    def _diff_commonPrefix_indexed(self, text1, text2, start1, end1, start2, end2):
        """Index-based common prefix detection."""
        len1, len2 = end1 - start1, end2 - start2
        if len1 == 0 or len2 == 0 or text1[start1] != text2[start2]:
            return 0
        
        # Binary search for common prefix length
        pointermin = 0
        pointermax = min(len1, len2)
        pointermid = pointermax
        pointerstart = 0
        
        while pointermin < pointermid:
            # Compare ranges
            match = True
            for i in range(pointerstart, pointermid):
                if text1[start1 + i] != text2[start2 + i]:
                    match = False
                    break
            
            if match:
                pointermin = pointermid
                pointerstart = pointermin
            else:
                pointermax = pointermid
            pointermid = (pointermax + pointermin) // 2
        
        return pointermid
    
    def _diff_commonSuffix_indexed(self, text1, text2, start1, end1, start2, end2):
        """Index-based common suffix detection."""
        len1, len2 = end1 - start1, end2 - start2
        if len1 == 0 or len2 == 0 or text1[end1 - 1] != text2[end2 - 1]:
            return 0
        
        # Binary search for common suffix length
        pointermin = 0
        pointermax = min(len1, len2)
        pointermid = pointermax
        pointerend = 0
        
        while pointermin < pointermid:
            # Compare suffix ranges
            match = True
            for i in range(pointermid - pointerend):
                if text1[end1 - pointermid + i] != text2[end2 - pointermid + i]:
                    match = False
                    break
            
            if match:
                pointermin = pointermid
                pointerend = pointermin
            else:
                pointermax = pointermid
            pointermid = (pointermax + pointermin) // 2
        
        return pointermid

    def diff_cleanupMerge(self, diffs):
        """Reorder and merge like edit sections. Merge equalities.
        Any edit section can move as long as it doesn't cross an equality.
        """
        if not diffs:
            return
        # Ensure all diff tuples contain proper lists
        # for i in range(len(diffs)):
        #     if not isinstance(diffs[i][1], list):
        #         # Convert to list if not already
        #         if diffs[i][1] is None:
        #             diffs[i] = (diffs[i][0], [])
        #         else:
        #             diffs[i] = (diffs[i][0], [diffs[i][1]])            
        diffs.append((self.DIFF_EQUAL, []))  # Add a dummy entry at the end.
        pointer = 0
        count_delete = 0
        count_insert = 0
        text_delete = []
        text_insert = []
        while pointer < len(diffs):
            if diffs[pointer][0] == self.DIFF_INSERT:
                count_insert += 1
                # Safely extend text_insert
                if isinstance(diffs[pointer][1], list):
                    text_insert.extend(diffs[pointer][1])
                else:
                    text_insert.append(diffs[pointer][1])
                pointer += 1
            elif diffs[pointer][0] == self.DIFF_DELETE:
                count_delete += 1
                # Safely extend text_delete
                if isinstance(diffs[pointer][1], list):
                    text_delete.extend(diffs[pointer][1])
                else:
                    text_delete.append(diffs[pointer][1])
                pointer += 1
            elif diffs[pointer][0] == self.DIFF_EQUAL:
                # Upon reaching an equality, check for prior redundancies.
                if count_delete + count_insert > 1:
                    if count_delete != 0 and count_insert != 0:
                        # Factor out any common prefixes.
                        commonlength = self.diff_commonPrefix(text_insert, text_delete)
                        if commonlength != 0:
                            x = pointer - count_delete - count_insert - 1
                            if x >= 0 and diffs[x][0] == self.DIFF_EQUAL:
                                # Ensure we're working with lists for concatenation
                                prefix = text_insert[:commonlength]
                                if not isinstance(diffs[x][1], list):
                                    diffs[x] = (diffs[x][0], [diffs[x][1]])
                                diffs[x] = (diffs[x][0], diffs[x][1] + prefix)
                            else:
                                diffs.insert(0, (self.DIFF_EQUAL, text_insert[:commonlength]))
                                pointer += 1
                            text_insert = text_insert[commonlength:]
                            text_delete = text_delete[commonlength:]
                        # Factor out any common suffixes.
                        commonlength = self.diff_commonSuffix(text_insert, text_delete)
                        if commonlength != 0:
                            # For lists, we need to handle suffix differently
                            suffix = text_insert[-commonlength:]
                            # Ensure we're working with lists for concatenation
                            if not isinstance(diffs[pointer][1], list):
                                diffs[pointer] = (diffs[pointer][0], [diffs[pointer][1]])
                            diffs[pointer] = (diffs[pointer][0], suffix + diffs[pointer][1])
                            text_insert = text_insert[:-commonlength]
                            text_delete = text_delete[:-commonlength]
                    # Delete the offending records and add the merged ones.
                    new_ops = []
                    if len(text_delete) != 0:
                        new_ops.append((self.DIFF_DELETE, text_delete))
                    if len(text_insert) != 0:
                        new_ops.append((self.DIFF_INSERT, text_insert))
                    pointer -= count_delete + count_insert
                    diffs[pointer: pointer + count_delete + count_insert] = new_ops
                    pointer += len(new_ops) + 1
                elif pointer != 0 and diffs[pointer - 1][0] == self.DIFF_EQUAL:
                    # Merge this equality with the previous one.
                    # Ensure we're working with lists for concatenation
                    if not isinstance(diffs[pointer - 1][1], list):
                        diffs[pointer - 1] = (diffs[pointer - 1][0], [diffs[pointer - 1][1]])
                    if not isinstance(diffs[pointer][1], list):
                        diffs[pointer] = (diffs[pointer][0], [diffs[pointer][1]])
                    diffs[pointer - 1] = (diffs[pointer - 1][0],
                                          diffs[pointer - 1][1] + diffs[pointer][1])
                    del diffs[pointer]
                else:
                    pointer += 1

                count_insert = 0
                count_delete = 0
                text_delete = []
                text_insert = []

        if not diffs[-1][1]:  # If the last entry is an empty list
            diffs.pop()  # Remove the dummy entry at the end.

        # Second pass: look for single edits surrounded on both sides by equalities
        # which can be shifted sideways to eliminate an equality.
        # e.g: A<ins>BA</ins>C -> <ins>AB</ins>AC
        changes = False
        pointer = 1
        # Intentionally ignore the first and last element (don't need checking).
        while pointer < len(diffs) - 1:
            if (diffs[pointer - 1][0] == self.DIFF_EQUAL and
                diffs[pointer + 1][0] == self.DIFF_EQUAL):
                # This is a single edit surrounded by equalities.
                # For lists, we need to adapt the endswith/startswith logic
                if self._list_endswith(diffs[pointer][1], diffs[pointer - 1][1]):
                    # Shift the edit over the previous equality.
                    if diffs[pointer - 1][1]:
                        prefix_len = len(diffs[pointer - 1][1])
                        new_edit = diffs[pointer - 1][1] + diffs[pointer][1][:-prefix_len]
                        diffs[pointer] = (diffs[pointer][0], new_edit)
                        diffs[pointer + 1] = (diffs[pointer + 1][0],
                                              diffs[pointer - 1][1] + diffs[pointer + 1][1])
                    del diffs[pointer - 1]
                    changes = True
                elif self._list_startswith(diffs[pointer][1], diffs[pointer + 1][1]):
                    # Shift the edit over the next equality.
                    suffix_len = len(diffs[pointer + 1][1])
                    diffs[pointer - 1] = (diffs[pointer - 1][0],
                                          diffs[pointer - 1][1] + diffs[pointer + 1][1])
                    new_edit = diffs[pointer][1][suffix_len:] + diffs[pointer + 1][1]
                    diffs[pointer] = (diffs[pointer][0], new_edit)
                    del diffs[pointer + 1]
                    changes = True
            pointer += 1

        # If shifts were made, the diff needs reordering and another shift sweep.
        if changes:
            self.diff_cleanupMerge(diffs)

    def _list_endswith(self, list1, list2):
        """Check if list1 ends with list2."""
        if not list2 or len(list2) > len(list1):
            return False
        return list1[-len(list2):] == list2

    def _list_startswith(self, list1, list2):
        """Check if list1 starts with list2."""
        if not list2 or len(list2) > len(list1):
            return False
        return list1[:len(list2)] == list2


def diff_bisect_cpp(self, text1, text2):
    """C++ implementation of diff_bisect via ctypes."""
    if not cpp_lib_loaded:
        # Fallback to Python implementation
        return self.diff_bisect(text1, text2)
    
    # Convert to numpy arrays if not already
    text1_array = np.array(text1, dtype=np.int32)
    text2_array = np.array(text2, dtype=np.int32)
    
    # Prepare output variables
    x_out = ctypes.c_int()
    y_out = ctypes.c_int()
    
    # Call C++ function
    start = time.time()
    result = myers_lib.myers_diff_bisect(
        text1_array.ctypes.data_as(ctypes.POINTER(ctypes.c_int)),
        len(text1),
        text2_array.ctypes.data_as(ctypes.POINTER(ctypes.c_int)),
        len(text2),
        ctypes.byref(x_out),
        ctypes.byref(y_out)
    )
    end = time.time()
    global total_meyer_cpp
    total_meyer_cpp +=  (end - start)
    if (total_meyer_cpp < 7):
        print ("Current total_meyer_cpp: ", total_meyer_cpp)
    else:
        
        print ("beyond threshold, ", total_meyer_cpp, " seconds") 
        time.sleep(10)
    if result != 0:
        # Error occurred
        return [(self.DIFF_DELETE, text1), (self.DIFF_INSERT, text2)]
    
    x, y = x_out.value, y_out.value
    
    if x == -1 and y == -1:
        # No snake found
        return [(self.DIFF_DELETE, text1), (self.DIFF_INSERT, text2)]
    else:
        # Snake found - call the split function
        return self.diff_bisectSplit(text1, text2, x, y)


def _optimized_myers_diff_internal(file1_lines, file2_lines, discard=True):
    """Internal function to compute diff ops (A vs B) using GNU diff approach."""
    file_data = [FileData(file1_lines), FileData(file2_lines)]
    # TODO: Add minimal parameter option to myerDiff interface
    discard_confusing_lines(file_data, discard)
    build_undiscarded_arrays(file_data)

    # Run Myers algorithm on undiscarded lines using equivalence classes
    if file_data[0].nondiscarded_lines > 0 and file_data[1].nondiscarded_lines > 0:
        _run_myers_on_undiscarded(file_data)
    
    # Apply boundary shifting like GNU diff
    shift_boundaries(file_data)
    
    # Build the final diff operations from the changed arrays
    final_diff_ops = build_diff_ops_from_changed_arrays(file_data)
    
    return final_diff_ops


def _run_myers_on_undiscarded(file_data: List[FileData]) -> None:
    """Run Myers algorithm on undiscarded equivalence classes, mimicking GNU diff compareseq."""
    xvec = file_data[0].undiscarded  # Equivalence classes for file 0
    yvec = file_data[1].undiscarded  # Equivalence classes for file 1
    
    # Ensure we have valid undiscarded arrays
    if xvec is None or yvec is None:
        return
    
    # Simple implementation of Myers algorithm on equivalence classes
    # This directly compares equivalence class values
    m = len(xvec)
    n = len(yvec)
    # Use a simple DP approach for small sequences, otherwise fall back to DMP
    _myers_with_dmp(file_data, xvec, yvec)
        
def diff_main_cpp(text1, text2):
    """C++ implementation of full diff computation via ctypes."""
    if not cpp_lib_loaded:
        # Fallback to Python implementation
        diff = DiffMatchPatch()
        return diff.diff_main(text1, text2)
    
    try:
        print(f"diff_main_cpp called with text1 length: {len(text1)}, text2 length: {len(text2)}")
        
        # Convert to numpy arrays
        text1_array = np.array(text1, dtype=np.int32)
        text2_array = np.array(text2, dtype=np.int32)
        print("Successfully created numpy arrays")
        
        # Output parameters
        operations = ctypes.POINTER(ctypes.c_int)()
        data = ctypes.POINTER(ctypes.POINTER(ctypes.c_int))()
        data_lengths = ctypes.POINTER(ctypes.c_int)()
        result_count = ctypes.c_int()
        
        print("Created output parameters")
        
        # Call C++ function
        print(f"Calling myers_diff_compute with lengths: {len(text1)}, {len(text2)}")
        result = myers_lib.myers_diff_compute(
            text1_array.ctypes.data_as(ctypes.POINTER(ctypes.c_int)),
            len(text1),
            text2_array.ctypes.data_as(ctypes.POINTER(ctypes.c_int)),
            len(text2),
            ctypes.byref(operations),
            ctypes.byref(data),
            ctypes.byref(data_lengths),
            ctypes.byref(result_count)
        )
        print(f"myers_diff_compute returned: {result}")
        
        if result != 0:
            # Error occurred - fallback to Python
            if result == -2:
                print(f"C++ diff failed: Memory allocation error (input sizes: {len(text1)}, {len(text2)})")
            else:
                print(f"C++ diff failed with error code: {result}")
            diff = DiffMatchPatch()
            return diff.diff_main(text1, text2)
        
        # Convert results back to Python format
        diffs = []
        for i in range(result_count.value):
            op = operations[i]
            length = data_lengths[i]
            
            # Extract data array - handle nullptr case
            if length == 0 or not data[i]:
                data_array = []
            else:
                data_array = [data[i][j] for j in range(length)]
            diffs.append((op, data_array))
        
        # Free memory
        myers_lib.myers_diff_free(operations, data, data_lengths, result_count.value)
        
        return diffs
        
    except Exception as e:
        print(f"C++ diff failed, falling back to Python: {e}")
        # Fallback to Python implementation
        diff = DiffMatchPatch()
        return diff.diff_main(text1, text2)

def _myers_with_dmp(file_data: List[FileData], xvec, yvec) -> None:
    """Myers algorithm using DiffMatchPatch for larger sequences."""
    # Since we're now using Python lists directly, no need for conversion
    a_list = xvec
    b_list = yvec
    start = time.time()
    
    # Always create a diff object to access constants
    diff = DiffMatchPatch()
    
    # Use C++ implementation if available, otherwise fallback to Python
    if False:
        print("Using C++ Diff class implementation")
        diffs = diff_main_cpp(a_list, b_list)
    else:
        print("Using Python DiffMatchPatch implementation")
        diffs = diff.diff_main(a_list, b_list)
    
    end = time.time()
    global total_meyer_python
    print ("Myer Diff algorithm took", end - start, "seconds")
    print ("myer diff C++ time:", total_meyer_cpp)
    # Mark changes in the undiscarded space
    a_idx = 0
    b_idx = 0
    
    for op, elements in diffs:
        if op == diff.DIFF_EQUAL:
            # Skip equal elements
            a_idx += len(elements)
            b_idx += len(elements) 
        elif op == diff.DIFF_DELETE:
            # Mark these lines as deleted in file 0
            for _ in elements:
                if (file_data[0].realindexes is not None and 
                    a_idx < len(file_data[0].realindexes)):
                    orig_idx = file_data[0].realindexes[a_idx]
                    file_data[0].changed[orig_idx] = True
                a_idx += 1
        elif op == diff.DIFF_INSERT:
            # Mark these lines as inserted in file 1
            for _ in elements:
                if (file_data[1].realindexes is not None and 
                    b_idx < len(file_data[1].realindexes)):
                    orig_idx = file_data[1].realindexes[b_idx]
                    file_data[1].changed[orig_idx] = True
                b_idx += 1


def optimized_myers_diff(file1_lines, file2_lines, discard=True):
    """
    Master function that computes diff and formats it.
    
    Args:
        file1_lines: Lines from the first file
        file2_lines: Lines from the second file  
        discard: Whether to discard confusing lines during diff computation (default: True)
    """
    # Compute diff internally
    a_vs_b_ops = _optimized_myers_diff_internal(file1_lines, file2_lines, discard)

    formatted_results = format_diff_ops_to_blocks(
        a_vs_b_ops, file1_lines, file2_lines  # Pass original lines A and B
    )
    return formatted_results


def compute_file_diff(file1_path, file2_path, discard=True, html=False, output_path=None):
    """
    Compute the Myers diff between two files and optionally generate output.
    
    This is the main external interface for computing diffs between files.
    
    Args:
        file1_path: Path to the first file
        file2_path: Path to the second file  
        discard: Whether to discard confusing lines during diff computation (default: True)
        html: Whether to generate HTML visualization (default: False)
        output_path: Path for output file. If None, uses default names based on format
        
    Returns:
        If html=True: Tuple of (diff_blocks, output_file_path)
        If html=False: diff_blocks (and optionally writes to text file if output_path provided)
    """
    file1_lines = read_file_lines(file1_path)
    file2_lines = read_file_lines(file2_path)
    
    print(f"File 1: {len(file1_lines)} lines")
    print(f"File 2: {len(file2_lines)} lines")
    
    # Compute diff
    start = time.time()
    diff_result = optimized_myers_diff(file1_lines, file2_lines, discard)
    end = time.time()
    
    print(f"Diff completed in {end - start:.4f} seconds")
    print(f"Found {sum(len(block) for block in diff_result)} changes in {len(diff_result)} blocks")
    
    if html:
        # Generate HTML visualization
        print(f"Generating HTML visualization...")
        html_start = time.time()
        if output_path is None:
            output_path = "diff_visualization.html"
        html_file_path = generate_html_diff_visualization(
            file1_path, file2_path, diff_result, file1_lines, file2_lines,
            output_path
        )
        html_end = time.time()
        
        print(f"HTML visualization generated in {html_end - html_start:.4f} seconds")
        print(f"HTML file saved to: {html_file_path}")
        
        return diff_result, html_file_path
    else:
        # Optionally write to text file
        if output_path is not None:
            print(f"Writing diff to text file...")
            text_start = time.time()
            with open(output_path, 'w', encoding='utf-8') as f:
                for block in diff_result:
                    for element in block:
                        f.write(str(element) + '\n')
            text_end = time.time()
            
            print(f"Text diff written in {text_end - text_start:.4f} seconds")
            print(f"Text file saved to: {output_path}")
        
        return diff_result

def diff_and_transform(file1_path, file2_path, discard=True):
    """
    Standalone function that performs diff computation and applies transformation.
    
    This function computes the diff between two files and then applies the diff
    to transform the first file to match the second file, verifying the transformation.
    
    Args:
        file1_path: Path to the first file (source)
        file2_path: Path to the second file (target)
        discard: Whether to discard confusing lines during diff computation (default: True)
        
    Returns:
        Tuple of (diff_blocks, transformed_lines, verification_success)
        - diff_blocks: The computed diff blocks
        - transformed_lines: The result of applying diff to file1
        - verification_success: Boolean indicating if transformation matches file2
    """
    # Read the files
    file1_lines = read_file_lines(file1_path)
    file2_lines = read_file_lines(file2_path)
    
    print(f"Source file: {len(file1_lines)} lines")
    print(f"Target file: {len(file2_lines)} lines")
    
    # Compute the diff
    start_time = time.time()
    diff_blocks = optimized_myers_diff(file1_lines, file2_lines, discard)
    diff_time = time.time() - start_time
    
    print(f"Diff computation completed in {diff_time:.4f} seconds")
    print(f"Found {len(diff_blocks)} diff blocks")
    
    # Apply the transformation
    start_time = time.time()
    transformed_lines = apply_formatted_diff(
        file1_lines, 
        diff_blocks, 
        file2_lines, 
        trace=False
    )
    transform_time = time.time() - start_time
    
    print(f"Transformation completed in {transform_time:.4f} seconds")
    print(f"Transformed result: {len(transformed_lines)} lines")
    
    # Verify the transformation
    verification_success = transformed_lines == file2_lines
    
    if verification_success:
        print("Success: Transformation matches target file perfectly!")
    else:
        print("Warning: Transformation does not match target file")
        print(f"Expected {len(file2_lines)} lines, got {len(transformed_lines)} lines")
        
        # Show first few differences for debugging
        max_check = min(len(file2_lines), len(transformed_lines), 10)
        diff_count = 0
        for i in range(max_check):
            if i < len(file2_lines) and i < len(transformed_lines):
                if file2_lines[i] != transformed_lines[i]:
                    print(f"Line {i+1} differs:")
                    print(f"  Expected: {repr(file2_lines[i])}")
                    print(f"  Got:      {repr(transformed_lines[i])}")
                    diff_count += 1
                    if diff_count >= 3:  # Limit output
                        break
    
    return diff_blocks, transformed_lines, verification_success

def run_test_cases():
    """Run diff_and_transform on all test cases and report results."""
    # Windows path
    test_dir = r"C:\Users\<USER>\Documents\DiffTestCases\Case1\test_case_003\test_cases"
    # Linux path (uncomment for Linux)
    # test_dir = "/home/<USER>/Documents/DiffTestCases/Case1/test_case_003/test_cases"
    
    # Cross-platform path (alternative - uncomment to use)
    # test_dir = os.path.join(os.path.expanduser("~"), "Documents", "DiffTestCases", "Case1", "test_case_003", "test_cases")
    
    # Check if directory exists
    if not os.path.isdir(test_dir):
        print(f"Error: Test directory not found: {test_dir}")
        return
        
    # Track statistics
    total_tests = 0
    successful_tests = 0
    
    # Iterate through each test case directory
    for folder in sorted(os.listdir(test_dir)):
        folder_path = os.path.join(test_dir, folder)
        
        # Skip if not a directory or doesn't match test_case_XX pattern
        if not os.path.isdir(folder_path) or not folder.startswith('test_case_'):
            continue
            
        # Construct paths to test files
        file_a = os.path.join(folder_path, 'a.txt')
        file_b = os.path.join(folder_path, 'b.txt')
        
        # Skip if either file is missing
        if not os.path.isfile(file_a) or not os.path.isfile(file_b):
            print(f"Skipping {folder}: Missing a.txt or b.txt")
            continue
            
        print(f"\n{'='*50}")
        print(f"Running test case: {folder}")
        print(f"{'='*50}")
        
        # Run the diff and transformation
        _, _, success = diff_and_transform(file_a, file_b, discard=True)
        
        total_tests += 1
        if success:
            successful_tests += 1
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Test Summary: {successful_tests}/{total_tests} tests passed")
    print(f"{'='*50}")
    
    return successful_tests, total_tests

if __name__ == "__main__":
    # Platform-specific path configuration
    if platform.system() == 'Windows':
        # Windows paths
        file1 = r"C:\Users\<USER>\Documents\DiffTestCases\Case9\V11.8"
        file2 = r"C:\Users\<USER>\Documents\DiffTestCases\Case9\64bit_SEAMSE.oue.run"
        
        
        file1 = r"C:\Users\<USER>\Documents\DiffTestCases\Case1\32bit_SEAMSE.oue.run"
        file2 = r"C:\Users\<USER>\Documents\DiffTestCases\Case1\64bit_SEAMSE.oue.run"
    else:
        # Linux/Unix paths (including WSL)
        file1 = "/mnt/c/Users/<USER>/Documents/DiffTestCases/Case1/32bit_SEAMSE.oue.run"
        file2 = "/mnt/c/Users/<USER>/Documents/DiffTestCases/Case1/64bit_SEAMSE.oue.run"
    
    # Alternative cross-platform approach using os.path.join (uncomment to use instead)
    # file1 = os.path.join(os.path.expanduser("~"), "Documents", "DiffTestCases", "Case1", "32bit_SEAMSE.oue.run")
    # file2 = os.path.join(os.path.expanduser("~"), "Documents", "DiffTestCases", "Case1", "64bit_SEAMSE.oue.run")
    
    output_file = "diff_results.txt"
    #test_myers_diff(file1, file2)

    # Run the test cases
    #run_test_cases()
    #diff_and_transform(file1, file2, discard=True)
    with open(output_file, "w", encoding="utf-8") as f:
        diff, html_output = compute_file_diff(file1, file2, discard=False, html=True, output_path="diff_visualization.html")
        #diff = compute_gnu_compatible_file_diff(file1, file2, minimal=False)
        for xx in diff:
            f.write(str(xx) + "\n")
