#include "myers_diff.hpp"
#include <algorithm>
#include <vector>
#include <cstring>

// Template implementation for Diff class
template<typename T>
void Diff<T>::ensure_V(size_t len) const {
    if (V1_.size() < len) {
        V1_.assign(len, -1);
        V2_.assign(len, -1);
    } else {
        std::fill(V1_.begin(), V1_.end(), -1);
        std::fill(V2_.begin(), V2_.end(), -1);
    }
}

template<typename T>
size_t Diff<T>::common_prefix_len(const Slice<T>& A, const Slice<T>& B) {
    size_t n = std::min(A.size(), B.size());
    size_t i = 0;
    while (i < n && A[i] == B[i]) ++i;
    return i;
}

template<typename T>
size_t Diff<T>::common_suffix_len(const Slice<T>& A, const Slice<T>& B) {
    size_t n = std::min(A.size(), B.size());
    size_t i = 0;
    while (i < n && A[A.size() - 1 - i] == B[B.size() - 1 - i]) ++i;
    return i;
}

template<typename T>
DiffResult<T> Diff<T>::diff_main(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_main_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_main_impl(Slice<T> A, Slice<T> B) {
    // Check for equality (speedup)
    if (A.size() == B.size() &&
        std::equal(A.buf->begin() + A.begin,
                   A.buf->begin() + A.end,
                   B.buf->begin() + B.begin)) {
        if (!A.empty()) {
            return {{DIFF_EQUAL, {A.buf->begin() + A.begin,
                                  A.buf->begin() + A.end}}};
        }
        return {};
    }

    // Trim off common prefix (speedup)
    size_t prefix = common_prefix_len(A, B);
    std::vector<T> commonprefix;
    if (prefix > 0) {
        commonprefix.assign(A.buf->begin() + A.begin,
                           A.buf->begin() + A.begin + prefix);
    }
    
    Slice<T> A_trimmed{A.buf, A.begin + prefix, A.end};
    Slice<T> B_trimmed{B.buf, B.begin + prefix, B.end};

    // Trim off common suffix (speedup)
    size_t suffix = common_suffix_len(A_trimmed, B_trimmed);
    std::vector<T> commonsuffix;
    if (suffix > 0) {
        commonsuffix.assign(A_trimmed.buf->begin() + A_trimmed.end - suffix,
                           A_trimmed.buf->begin() + A_trimmed.end);
    }
    
    Slice<T> A_mid{A_trimmed.buf, A_trimmed.begin, A_trimmed.end - suffix};
    Slice<T> B_mid{B_trimmed.buf, B_trimmed.begin, B_trimmed.end - suffix};

    // Compute the diff on the middle block
    DiffResult<T> diffs = diff_compute_impl(A_mid, B_mid);

    // Restore the prefix and suffix
    if (!commonprefix.empty()) {
        diffs.insert(diffs.begin(), {DIFF_EQUAL, commonprefix});
    }
    if (!commonsuffix.empty()) {
        diffs.push_back({DIFF_EQUAL, commonsuffix});
    }

    return diffs;
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_compute_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute_impl(Slice<T> A, Slice<T> B) {
    if (A.empty()) {
        return {{DIFF_INSERT, {B.buf->begin() + B.begin, B.buf->begin() + B.end}}};
    }
    if (B.empty()) {
        return {{DIFF_DELETE, {A.buf->begin() + A.begin, A.buf->begin() + A.end}}};
    }

    // Use bisect algorithm
    return diff_bisect_impl(A, B);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_bisect_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect_impl(Slice<T> A, Slice<T> B) {
    // Cache the slice lengths
    size_t text1_length = A.size();
    size_t text2_length = B.size();
    size_t max_d = (text1_length + text2_length + 1) / 2;
    size_t v_offset = max_d;
    size_t v_length = 2 * max_d + 1 + 2; // +2 for guard cells
    
    // Ensure V buffer capacity
    ensure_V(v_length);
    
    // Optimize: precompute integer variables
    int text1_len = static_cast<int>(text1_length);
    int text2_len = static_cast<int>(text2_length);
    int max_d_i = static_cast<int>(max_d);
    int voff = static_cast<int>(v_offset);
    
    V1_[voff + 1] = 0;
    V2_[voff + 1] = 0;
    int delta = text1_len - text2_len;
    bool front = (delta % 2 != 0);
    
    int k1start = 0, k1end = 0, k2start = 0, k2end = 0;
    
    for (int d = 0; d < max_d_i; ++d) {
        // Walk the front path one step
        for (int k1 = -d + k1start; k1 < d + 1 - k1end; k1 += 2) {
            int k1_offset = voff + k1;
            int x1;
            // Choose predecessor
            if (k1 == -d || (k1 != d && V1_[k1_offset - 1] < V1_[k1_offset + 1])) {
                x1 = V1_[k1_offset + 1];
            } else {
                x1 = V1_[k1_offset - 1] + 1;
            }
            int y1 = x1 - k1;
            
            // Advance along the diagonal
            while (x1 < text1_len && y1 < text2_len && A[x1] == B[y1]) {
                ++x1; ++y1;
            }
            
            V1_[k1_offset] = x1;
            
            if (x1 > text1_len) {
                k1end += 2;
            } else if (y1 > text2_len) {
                k1start += 2;
            } else if (front) {
                int k2_offset = voff + delta - k1;
                if (k2_offset >= 0 && k2_offset < static_cast<int>(v_length) && V2_[k2_offset] != -1) {
                    int x2 = text1_len - V2_[k2_offset];
                    if (x1 >= x2) {
                        return diff_bisectSplit_impl(A, B, static_cast<size_t>(x1), static_cast<size_t>(y1));
                    }
                }
            }
        }

        // Walk the reverse path one step
        for (int k2 = -d + k2start; k2 < d + 1 - k2end; k2 += 2) {
            int k2_offset = voff + k2;
            int x2;
            if (k2 == -d || (k2 != d && V2_[k2_offset - 1] < V2_[k2_offset + 1])) {
                x2 = V2_[k2_offset + 1];
            } else {
                x2 = V2_[k2_offset - 1] + 1;
            }
            int y2 = x2 - k2;
            
            while (x2 < text1_len && y2 < text2_len && A[text1_len - x2 - 1] == B[text2_len - y2 - 1]) {
                ++x2; ++y2;
            }
            
            V2_[k2_offset] = x2;
            
            if (x2 > text1_len) {
                k2end += 2;
            } else if (y2 > text2_len) {
                k2start += 2;
            } else if (!front) {
                int k1_off = voff + delta - k2;
                if (k1_off >= 0 && k1_off < static_cast<int>(v_length) && V1_[k1_off] != -1) {
                    int x1 = V1_[k1_off];
                    int y1 = voff + x1 - k1_off;
                    int x2_mirror = text1_len - x2;
                    if (x1 >= x2_mirror) {
                        return diff_bisectSplit_impl(A, B, static_cast<size_t>(x1), static_cast<size_t>(y1));
                    }
                }
            }
        }
    }

    // Diff took too long and hit the deadline or no commonality at all
    return {{DIFF_DELETE, {A.buf->begin() + A.begin, A.buf->begin() + A.end}}, 
            {DIFF_INSERT, {B.buf->begin() + B.begin, B.buf->begin() + B.end}}};
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisectSplit(const std::vector<T>& text1, const std::vector<T>& text2, 
                                        size_t x, size_t y) {
    Slice<T> A = make_slice(text1);
    Slice<T> B = make_slice(text2);
    return diff_bisectSplit_impl(A, B, x, y);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisectSplit_impl(Slice<T> A, Slice<T> B, size_t x, size_t y) {
    Slice<T> A_left{A.buf, A.begin, A.begin + x};
    Slice<T> B_left{B.buf, B.begin, B.begin + y};
    Slice<T> A_right{A.buf, A.begin + x, A.end};
    Slice<T> B_right{B.buf, B.begin + y, B.end};

    // Compute both diffs serially
    DiffResult<T> diffs = diff_main_impl(A_left, B_left);
    DiffResult<T> diffsb = diff_main_impl(A_right, B_right);

    // Concatenate results
    diffs.insert(diffs.end(), diffsb.begin(), diffsb.end());
    return diffs;
}

template<typename T>
std::pair<size_t, size_t> Diff<T>::middle_snake(const std::vector<T>& text1, const std::vector<T>& text2) {
    Slice<T> A = make_slice(text1);
    Slice<T> B = make_slice(text2);
    
    // Cache the slice lengths
    size_t text1_length = A.size();
    size_t text2_length = B.size();
    size_t max_d = (text1_length + text2_length + 1) / 2;
    size_t v_offset = max_d;
    size_t v_length = 2 * max_d + 1 + 2; // +2 for guard cells
    
    // Ensure V buffer capacity
    ensure_V(v_length);
    
    // Optimize: precompute integer variables
    int text1_len = static_cast<int>(text1_length);
    int text2_len = static_cast<int>(text2_length);
    int max_d_i = static_cast<int>(max_d);
    int voff = static_cast<int>(v_offset);
    
    V1_[voff + 1] = 0;
    V2_[voff + 1] = 0;
    int delta = text1_len - text2_len;
    bool front = (delta % 2 != 0);
    
    int k1start = 0, k1end = 0, k2start = 0, k2end = 0;
    
    for (int d = 0; d < max_d_i; ++d) {
        // Walk the front path one step
        for (int k1 = -d + k1start; k1 < d + 1 - k1end; k1 += 2) {
            int k1_offset = voff + k1;
            int x1;
            // Choose predecessor
            if (k1 == -d || (k1 != d && V1_[k1_offset - 1] < V1_[k1_offset + 1])) {
                x1 = V1_[k1_offset + 1];
            } else {
                x1 = V1_[k1_offset - 1] + 1;
            }
            int y1 = x1 - k1;
            
            // Advance along the diagonal
            while (x1 < text1_len && y1 < text2_len && A[x1] == B[y1]) {
                ++x1; ++y1;
            }
            
            V1_[k1_offset] = x1;
            
            if (x1 > text1_len) {
                k1end += 2;
            } else if (y1 > text2_len) {
                k1start += 2;
            } else if (front) {
                int k2_offset = voff + delta - k1;
                if (k2_offset >= 0 && k2_offset < static_cast<int>(v_length) && V2_[k2_offset] != -1) {
                    int x2 = text1_len - V2_[k2_offset];
                    if (x1 >= x2) {
                        return {static_cast<size_t>(x1), static_cast<size_t>(y1)};
                    }
                }
            }
        }

        // Walk the reverse path one step
        for (int k2 = -d + k2start; k2 < d + 1 - k2end; k2 += 2) {
            int k2_offset = voff + k2;
            int x2;
            if (k2 == -d || (k2 != d && V2_[k2_offset - 1] < V2_[k2_offset + 1])) {
                x2 = V2_[k2_offset + 1];
            } else {
                x2 = V2_[k2_offset - 1] + 1;
            }
            int y2 = x2 - k2;
            
            while (x2 < text1_len && y2 < text2_len && A[text1_len - x2 - 1] == B[text2_len - y2 - 1]) {
                ++x2; ++y2;
            }
            
            V2_[k2_offset] = x2;
            
            if (x2 > text1_len) {
                k2end += 2;
            } else if (y2 > text2_len) {
                k2start += 2;
            } else if (!front) {
                int k1_off = voff + delta - k2;
                if (k1_off >= 0 && k1_off < static_cast<int>(v_length) && V1_[k1_off] != -1) {
                    int x1 = V1_[k1_off];
                    int y1 = voff + x1 - k1_off;
                    int x2_mirror = text1_len - x2;
                    if (x1 >= x2_mirror) {
                        return {static_cast<size_t>(x1), static_cast<size_t>(y1)};
                    }
                }
            }
        }
    }

    // No snake found within max_d iterations
    return {static_cast<size_t>(-1), static_cast<size_t>(-1)};
}

// Updated prefix/suffix detection using linear scan
template<typename T>
size_t Diff<T>::diff_commonPrefix(const std::vector<T>& text1, const std::vector<T>& text2) {
    size_t n = std::min(text1.size(), text2.size());
    size_t i = 0;
    while (i < n && text1[i] == text2[i]) ++i;
    return i;
}

template<typename T>
size_t Diff<T>::diff_commonSuffix(const std::vector<T>& text1, const std::vector<T>& text2) {
    size_t n = std::min(text1.size(), text2.size());
    size_t i = 0;
    while (i < n && text1[text1.size() - 1 - i] == text2[text2.size() - 1 - i]) ++i;
    return i;
}

template<typename T>
bool Diff<T>::list_endswith(const std::vector<T>& list1, const std::vector<T>& list2) {
    if (list2.empty() || list2.size() > list1.size()) {
        return false;
    }
    return std::equal(list2.begin(), list2.end(), list1.end() - list2.size());
}

template<typename T>
bool Diff<T>::list_startswith(const std::vector<T>& list1, const std::vector<T>& list2) {
    if (list2.empty() || list2.size() > list1.size()) {
        return false;
    }
    return std::equal(list2.begin(), list2.end(), list1.begin());
}

template<typename T>
int Diff<T>::find_subsequence(typename std::vector<T>::const_iterator longtext_begin,
                              typename std::vector<T>::const_iterator longtext_end,
                              typename std::vector<T>::const_iterator shorttext_begin,
                              typename std::vector<T>::const_iterator shorttext_end) {
    if (shorttext_begin == shorttext_end) {
        return 0;
    }
    
    size_t longtext_length = std::distance(longtext_begin, longtext_end);
    size_t shorttext_length = std::distance(shorttext_begin, shorttext_end);
    
    if (longtext_length < shorttext_length) {
        return -1;
    }
    
    for (size_t i = 0; i <= longtext_length - shorttext_length; ++i) {
        if (std::equal(shorttext_begin, shorttext_end, longtext_begin + i)) {
            return static_cast<int>(i);
        }
    }
    
    return -1;
}

// Updated range-based versions as thin wrappers
template<typename T>
DiffResult<T> Diff<T>::diff_main_range(typename std::vector<T>::const_iterator text1_begin,
                                       typename std::vector<T>::const_iterator text1_end,
                                       typename std::vector<T>::const_iterator text2_begin,
                                       typename std::vector<T>::const_iterator text2_end) {
    // Create temporary vectors for slicing - in practice, you could optimize this further
    // by implementing true iterator-based slices
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_main_impl(make_slice(text1_vec), make_slice(text2_vec));
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute_range(typename std::vector<T>::const_iterator text1_begin,
                                          typename std::vector<T>::const_iterator text1_end,
                                          typename std::vector<T>::const_iterator text2_begin,
                                          typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_compute_impl(make_slice(text1_vec), make_slice(text2_vec));
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect_range(typename std::vector<T>::const_iterator text1_begin,
                                         typename std::vector<T>::const_iterator text1_end,
                                         typename std::vector<T>::const_iterator text2_begin,
                                         typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_bisect_impl(make_slice(text1_vec), make_slice(text2_vec));
}

template<typename T>
size_t Diff<T>::diff_commonPrefix_range(typename std::vector<T>::const_iterator text1_begin,
                                        typename std::vector<T>::const_iterator text1_end,
                                        typename std::vector<T>::const_iterator text2_begin,
                                        typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_commonPrefix(text1_vec, text2_vec);
}

template<typename T>
size_t Diff<T>::diff_commonSuffix_range(typename std::vector<T>::const_iterator text1_begin,
                                        typename std::vector<T>::const_iterator text1_end,
                                        typename std::vector<T>::const_iterator text2_begin,
                                        typename std::vector<T>::const_iterator text2_end) {
    std::vector<T> text1_vec(text1_begin, text1_end);
    std::vector<T> text2_vec(text2_begin, text2_end);
    return diff_commonSuffix(text1_vec, text2_vec);
}

// Explicit template instantiation for int type
template class Diff<int>;

// Slimmer C API wrapper
extern "C" {

int myers_diff_bisect(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int* x_out, int* y_out) 
{
    if (!text1 || !text2 || !x_out || !y_out) {
        return -1;
    }
    
    try {
        // Convert C arrays to vectors
        std::vector<int> text1_vec(text1, text1 + text1_length);
        std::vector<int> text2_vec(text2, text2 + text2_length);
        
        // Use the new Diff class middle_snake helper
        Diff<int> diff;
        auto result = diff.middle_snake(text1_vec, text2_vec);
        
        *x_out = static_cast<int>(result.first);
        *y_out = static_cast<int>(result.second);
        return 0;
        
    } catch (const std::exception& e) {
        return -1;
    }
}

// New C API function for full diff computation
MYERS_API int myers_diff_compute(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int** operations_out,   // Output: array of operation types
    int*** data_out,        // Output: array of data arrays
    int** data_lengths_out, // Output: array of data lengths
    int* result_count_out   // Output: number of diff operations
) {
    if (!text1 || !text2 || !operations_out || !data_out || 
        !data_lengths_out || !result_count_out) {
        return -1;
    }
    
    try {
        // Check for negative lengths
        if (text1_length < 0 || text2_length < 0) {
            return -1;
        }
        
        // Check for extremely large inputs that might cause allocation failures
        const size_t MAX_SIZE = 100000000; // 100 million elements
        if (static_cast<size_t>(text1_length) > MAX_SIZE || 
            static_cast<size_t>(text2_length) > MAX_SIZE) {
            return -1;
        }
        
        // Convert C arrays to vectors
        std::vector<int> text1_vec(text1, text1 + text1_length);
        std::vector<int> text2_vec(text2, text2 + text2_length);
        
        // Use the new Diff class
        Diff<int> diff;
        DiffResult<int> result = diff.diff_main(text1_vec, text2_vec);
        
        // Convert result back to C arrays
        *result_count_out = static_cast<int>(result.size());
        
        // Handle empty result case
        if (*result_count_out == 0) {
            *operations_out = nullptr;
            *data_out = nullptr;
            *data_lengths_out = nullptr;
            return 0;
        }
        
        // Allocate arrays for results
        *operations_out = static_cast<int*>(malloc(*result_count_out * sizeof(int)));
        *data_out = static_cast<int**>(malloc(*result_count_out * sizeof(int*)));
        *data_lengths_out = static_cast<int*>(malloc(*result_count_out * sizeof(int)));
        
        if (!*operations_out || !*data_out || !*data_lengths_out) {
            // Cleanup on allocation failure
            if (*operations_out) free(*operations_out);
            if (*data_out) free(*data_out);
            if (*data_lengths_out) free(*data_lengths_out);
            *operations_out = nullptr;
            *data_out = nullptr;
            *data_lengths_out = nullptr;
            return -1;
        }
        
        for (int i = 0; i < *result_count_out; ++i) {
            (*operations_out)[i] = result[i].first;
            (*data_lengths_out)[i] = static_cast<int>(result[i].second.size());
            
            // Handle empty data case
            if ((*data_lengths_out)[i] == 0) {
                (*data_out)[i] = nullptr;
                continue;
            }
            
            // Allocate data array for this operation
            (*data_out)[i] = static_cast<int*>(malloc((*data_lengths_out)[i] * sizeof(int)));
            if (!(*data_out)[i]) {
                // Cleanup on allocation failure
                for (int j = 0; j < i; ++j) {
                    if ((*data_out)[j]) free((*data_out)[j]);
                }
                free(*operations_out);
                free(*data_out);
                free(*data_lengths_out);
                *operations_out = nullptr;
                *data_out = nullptr;
                *data_lengths_out = nullptr;
                return -1;
            }
            
            // Copy data safely
            for (int j = 0; j < (*data_lengths_out)[i]; ++j) {
                (*data_out)[i][j] = result[i].second[j];
            }
        }
        
        return 0;
        
    } catch (const std::bad_alloc& e) {
        // Memory allocation failure
        return -2;
    } catch (const std::exception& e) {
        // Other exceptions
        return -1;
    } catch (...) {
        // Unknown exception
        return -1;
    }
}

// Helper function to free memory allocated by myers_diff_compute
MYERS_API void myers_diff_free(
    int* operations,
    int** data,
    int* data_lengths,
    int result_count
) {
    if (data) {
        for (int i = 0; i < result_count; ++i) {
            if (data[i]) {
                free(data[i]);
            }
        }
        free(data);
    }
    if (operations) {
        free(operations);
    }
    if (data_lengths) {
        free(data_lengths);
    }
}

} // extern "C"
