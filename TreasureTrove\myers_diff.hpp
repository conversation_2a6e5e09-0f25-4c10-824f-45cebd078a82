#ifndef MYERS_DIFF_HPP
#define MYERS_DIFF_HPP

#include <vector>
#include <utility>
#include <iterator>
#include <algorithm>
#include <cstdint>

#ifdef _WIN32
    #ifdef MYERS_DIFF_EXPORTS
        #define MYERS_API __declspec(dllexport)
    #else
        #define MYERS_API __declspec(dllimport)
    #endif
#else
    #define MYERS_API
#endif

// Diff operation constants
const int DIFF_DELETE = -1;
const int DIFF_INSERT = 1;
const int DIFF_EQUAL = 0;

// Diff result type: vector of (operation, data) pairs
template<typename T>
using DiffResult = std::vector<std::pair<int, std::vector<T>>>;

// Lightweight "view" struct for zero-copy operations
template<typename T>
struct Slice {
    const std::vector<T>* buf;
    int32_t begin;
    int32_t end;          // one-past-the-last

    int32_t size()  const { return end - begin; }
    bool   empty() const { return begin == end; }
    const T& operator[](int32_t i) const { return (*buf)[begin + i]; }
};

// Helper to build a Slice from a whole vector
template<typename T>
Slice<T> make_slice(const std::vector<T>& v)
{ return {&v, 0, static_cast<int32_t>(v.size())}; }

// Generation-aware V buffer for optimized memory access
struct VecGen {
    std::vector<int32_t> data;          // V values
    std::vector<uint32_t> age;          // tag per cell
    uint32_t clock = 1;                 // monotonically increasing
    
    void ensure(int len) {
        if (data.size() < len) {
            data.resize(len, -1);
            age.resize(len, 0);
        }
        if (++clock == 0) {             // wrap-around, rare
            std::fill(age.begin(), age.end(), 0);
            clock = 1;
        }
    }
    
    void clear_cell(int idx) { age[idx] = clock; data[idx] = -1; }
    
    int32_t& operator[](int idx) {
        if (age[idx] != clock) { age[idx] = clock; data[idx] = -1; }
        return data[idx];
    }
};

template<typename T>
class Diff {
public:
    // Configuration parameters
    int LCS_MinLength = 4;      // Minimum length to consider LCS optimization
    double LCS_MinRatio = 0.3;  // Minimum ratio of LCS to longer text (30%)

    // Constructor
    Diff() = default;

    // Main diff interface
    DiffResult<T> diff_main(const std::vector<T>& text1, const std::vector<T>& text2);
    
    // Core diff computation methods
    DiffResult<T> diff_compute(const std::vector<T>& text1, const std::vector<T>& text2);
    DiffResult<T> diff_bisect(const std::vector<T>& text1, const std::vector<T>& text2);
    DiffResult<T> diff_bisectSplit(const std::vector<T>& text1, const std::vector<T>& text2, 
                                   int32_t x, int32_t y);

    // Optimized prefix/suffix detection using linear scan
    int32_t diff_commonPrefix(const std::vector<T>& text1, const std::vector<T>& text2);
    int32_t diff_commonSuffix(const std::vector<T>& text1, const std::vector<T>& text2);

    // Helper that returns middle snake coordinates for C API
    std::pair<int32_t, int32_t> middle_snake(const std::vector<T>& text1, const std::vector<T>& text2);

    // Range-based versions for zero-copy operations
    // DiffResult<T> diff_main_range(typename std::vector<T>::const_iterator text1_begin,
    //                               typename std::vector<T>::const_iterator text1_end,
    //                               typename std::vector<T>::const_iterator text2_begin,
    //                               typename std::vector<T>::const_iterator text2_end);
    
    // DiffResult<T> diff_compute_range(typename std::vector<T>::const_iterator text1_begin,
    //                                  typename std::vector<T>::const_iterator text1_end,
    //                                  typename std::vector<T>::const_iterator text2_begin,
    //                                  typename std::vector<T>::const_iterator text2_end);
    
    // DiffResult<T> diff_bisect_range(typename std::vector<T>::const_iterator text1_begin,
    //                                 typename std::vector<T>::const_iterator text1_end,
    //                                 typename std::vector<T>::const_iterator text2_begin,
    //                                 typename std::vector<T>::const_iterator text2_end);

    // int32_t diff_commonPrefix_range(typename std::vector<T>::const_iterator text1_begin,
    //                                typename std::vector<T>::const_iterator text1_end,
    //                                typename std::vector<T>::const_iterator text2_begin,
    //                                typename std::vector<T>::const_iterator text2_end);
    
    // int32_t diff_commonSuffix_range(typename std::vector<T>::const_iterator text1_begin,
    //                                typename std::vector<T>::const_iterator text1_end,
    //                                typename std::vector<T>::const_iterator text2_begin,
    //                                typename std::vector<T>::const_iterator text2_end);

    // Utility methods
    bool list_endswith(const std::vector<T>& list1, const std::vector<T>& list2);
    bool list_startswith(const std::vector<T>& list1, const std::vector<T>& list2);
    
    // Helper methods for containment checking
    int find_subsequence(typename std::vector<T>::const_iterator longtext_begin,
                        typename std::vector<T>::const_iterator longtext_end,
                        typename std::vector<T>::const_iterator shorttext_begin,
                        typename std::vector<T>::const_iterator shorttext_end);

private:

    // Internal slice-based implementations
    DiffResult<T> diff_main_impl(Slice<T> A, Slice<T> B);
    DiffResult<T> diff_compute_impl(Slice<T> A, Slice<T> B);
    DiffResult<T> diff_bisect_impl(Slice<T> A, Slice<T> B);
    DiffResult<T> diff_bisectSplit_impl(Slice<T> A, Slice<T> B, int32_t x, int32_t y);
    
    // Internal prefix/suffix helpers for slices
    int32_t common_prefix_len(const Slice<T>& A, const Slice<T>& B);
    int32_t common_suffix_len(const Slice<T>& A, const Slice<T>& B);
};

// C API compatibility layer
extern "C" {
    // Main diff_bisect function
    // Returns 0 on success, -1 on failure
    // Results are returned via x_out and y_out parameters
    MYERS_API int myers_diff_bisect(
        const int* text1,      // First sequence
        int text1_length,      // Length of first sequence
        const int* text2,      // Second sequence  
        int text2_length,      // Length of second sequence
        int* x_out,           // Output: x coordinate of middle snake
        int* y_out            // Output: y coordinate of middle snake
    );
    
    // Helper function to find subsequence
    // Returns index where shorttext starts in longtext, or -1 if not found
    MYERS_API int myers_find_subsequence(
        const int* longtext,
        int longtext_length,
        const int* shorttext,
        int shorttext_length
    );
    
    // Full diff computation function using new Diff class
    // Returns 0 on success, -1 on failure
    // Caller must free returned arrays using myers_diff_free()
    MYERS_API int myers_diff_compute(
        const int* text1, int text1_length,
        const int* text2, int text2_length,
        int** operations_out,   // Output: array of operation types (DIFF_DELETE, DIFF_INSERT, DIFF_EQUAL)
        int*** data_out,        // Output: array of data arrays
        int** data_lengths_out, // Output: array of data lengths
        int* result_count_out   // Output: number of diff operations
    );
    
    // Helper function to free memory allocated by myers_diff_compute
    MYERS_API void myers_diff_free(
        int* operations,
        int** data,
        int* data_lengths,
        int result_count
    );
}

#endif // MYERS_DIFF_HPP