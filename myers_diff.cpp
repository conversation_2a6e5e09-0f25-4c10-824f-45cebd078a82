#include "myers_diff.hpp"
#include <algorithm>
#include <vector>
#include <cstring>
#include <iostream>

#ifdef _OPENMP
#include <omp.h>
#endif

// Template implementation for Diff class
template<typename IndexT, typename T>
inline bool reverse_step(
        int32_t           k,        // current diagonal  (x-y)
        int32_t           d,        // current edit-distance level
        int32_t           v_off,    // offset for indexing Vf / Vr
        int32_t           N,        // length of sequence a[]
        int32_t           M,        // length of sequence b[]
        const T*          a,        // pointer to sequence a
        const T*          b,        // pointer to sequence b
        IndexT*           Vr,       // reverse frontier array (to UPDATE)
        const IndexT*     Vf,       // forward frontier  array (read-only here)
        int32_t           delta,    // N – M   (difference in lengths)
        bool              odd_delta,// true  ⇢  delta is odd
        std::pair<int32_t,int32_t>& middle) // OUT: crossing point
{
    //----------------------------------------------------------------
    // 1.  Furthest x reached on this diagonal at distance d
    //----------------------------------------------------------------
    const int idx = v_off + k;

    int32_t x = (k == -d) ? Vr[idx + 1]                    // hit bottom edge
              : (k !=  d && Vr[idx - 1] < Vr[idx + 1])     // choose “longer” path
              ? Vr[idx + 1]
              : Vr[idx - 1] + 1;

    int32_t y = x - k;    //----------------------------------------------------------------
    // 2.  Extend the snake (walk back while elements match)
    //----------------------------------------------------------------
    while (x < N && y < M &&
           a[N - 1 - x] == b[M - 1 - y])
    {
        ++x;
        ++y;
    }

    Vr[idx] = (IndexT)(x);   // store frontier cell

    //----------------------------------------------------------------
    // 3.  Have we met the forward frontier? (only when delta is even)
    //----------------------------------------------------------------
    if (!odd_delta) {
        const int32_t k_for = delta - k;          // opposite diagonal
        if (k_for >= -d && k_for <= d) {          // forward frontier has that diagonal
            const int32_t forX = Vf[v_off + k_for];
            const int32_t revX = N - x;           // translate reverse-x to forward grid
            if (forX >= revX) {                   // frontiers crossed ⇒ middle snake
                middle = {forX, forX - k_for};
                return true;                      // signal “found”
            }        }
    }
      return false;  // no crossing found at this step
}

template<typename IndexT, typename T>
inline bool forward_step(
        int32_t           k,        // current diagonal  (x-y)
        int32_t           d,        // current edit-distance level
        int32_t           v_off,    // offset for indexing Vf / Vr
        int32_t           N,        // length of sequence a[]
        int32_t           M,        // length of sequence b[]
        const T*          a,        // pointer to sequence a
        const T*          b,        // pointer to sequence b
        IndexT*           Vf,       // forward frontier array (to UPDATE)
        const IndexT*     Vr,       // reverse frontier array (read-only here)
        int32_t           delta,    // N – M   (difference in lengths)
        bool              odd_delta,// true  ⇢  delta is odd
        std::pair<int32_t,int32_t>& middle) // OUT: crossing point
{
    //----------------------------------------------------------------
    // 1.  Furthest x reached on this diagonal at distance d
    //----------------------------------------------------------------
    const int idx = v_off + k;

    int32_t x = (k == -d) ? Vf[idx + 1]                    // hit left edge
              : (k !=  d && Vf[idx - 1] < Vf[idx + 1])     // choose "longer" path
              ? Vf[idx + 1]
              : Vf[idx - 1] + 1;

    int32_t y = x - k;    //----------------------------------------------------------------
    // 2.  Extend the snake (walk forward while elements match)
    //----------------------------------------------------------------
    while (x < N && y < M && a[x] == b[y])
    {
        ++x;
        ++y;
    }

    Vf[idx] = (IndexT)(x);   // store frontier cell

    //----------------------------------------------------------------
    // 3.  Have we met the reverse frontier? (only when delta is odd)
    //----------------------------------------------------------------
    if (odd_delta) {
        const int32_t k_rev = delta - k;         // opposite diagonal
        if (k_rev >= -(d-1) && k_rev <= (d-1)) { // reverse frontier has that diagonal
            const int32_t revX = N - Vr[v_off + k_rev];
            if (x >= revX) {                     // frontiers crossed ⇒ middle snake
                middle = {x, y};
                return true;                     // signal "found"
            }
        }
    }
    
    return false;  // no crossing found at this step
}


template<typename T>
template<typename IndexT>
std::pair<int32_t, int32_t> Diff<T>::middle_snake_core(IndexT* Vf, IndexT* Vr,
                                                       int32_t v_len, int32_t N, int32_t M,
                                                       const T* a, const T* b)
{

    const IndexT v_off = (IndexT)((v_len - 3) / 2);  // Offset for indexing
    const IndexT delta = (IndexT)(N - M);
    const bool odd_delta = delta & 1;

    // Initialize the V arrays
    Vf[v_off + 1] = 0;
    Vr[v_off + 1] = 0;

    IndexT k1s = 0, k1e = 0, k2s = 0, k2e = 0;
    const IndexT max_d = (IndexT)((N + M + 1) / 2);

        for (IndexT d = 0; d <= max_d; ++d) {
        // -------- forward sweep with loop unrolling
        k1s = (k1s - 1 > -d) ? k1s - 1 : (IndexT)(-d);
        k1e = (k1e + 1 < d) ? k1e + 1 : (IndexT)(d);
        // k1s = std::max((IndexT)(-d), (IndexT)(k1s - 1));
        // k1e = std::min((IndexT)( d), (IndexT)(k1e + 1));

        // Process diagonals with OpenMP parallelization
        std::pair<int32_t, int32_t> result;
        bool found_solution = false;

#ifdef _OPENMP
        // Use OpenMP for parallel processing of diagonals
        #pragma omp parallel for schedule(dynamic, 4) shared(found_solution, result) if((k1e - k1s) > 8)
        for (IndexT k = k1s; k <= k1e; k += 2) {
            if (!found_solution) {  // Check if solution already found
                std::pair<int32_t, int32_t> local_result;
                if (forward_step<IndexT, T>(k, (int32_t)d, (int32_t)v_off, (int32_t)N, (int32_t)M, a, b, Vf, Vr, (int32_t)delta, odd_delta, local_result)) {
                    #pragma omp critical
                    {
                        if (!found_solution) {
                            found_solution = true;
                            result = local_result;
                        }
                    }
                }
            }
        }
#else
        // Fallback to sequential processing when OpenMP is not available
        for (IndexT k = k1s; k <= k1e; k += 2) {
            if (forward_step<IndexT, T>(k, (int32_t)d, (int32_t)v_off, (int32_t)N, (int32_t)M, a, b, Vf, Vr, (int32_t)delta, odd_delta, result))
                return result;
        }
#endif

        if (found_solution) {
            return result;
        }
        
        // -------- reverse sweep with loop unrolling
        // k2s = std::max((IndexT)(-d), (IndexT)(k2s - 1));
        // k2e = std::min((IndexT)( d), (IndexT)(k2e + 1));  

        k2s = (k2s - 1 > -d) ? k2s - 1 : (IndexT)(-d);
        k2e = (k2e + 1 < d) ? k2e + 1 : (IndexT)(d);

        // Process reverse diagonals with OpenMP parallelization
        found_solution = false;

#ifdef _OPENMP
        // Use OpenMP for parallel processing of reverse diagonals
        #pragma omp parallel for schedule(dynamic, 4) shared(found_solution, result) if((k2e - k2s) > 8)
        for (IndexT k = k2s; k <= k2e; k += 2) {
            if (!found_solution) {  // Check if solution already found
                std::pair<int32_t, int32_t> local_result;
                if (reverse_step<IndexT, T>(k, (int32_t)d, (int32_t)v_off, (int32_t)N, (int32_t)M, a, b, Vr, Vf, (int32_t)delta, odd_delta, local_result)) {
                    #pragma omp critical
                    {
                        if (!found_solution) {
                            found_solution = true;
                            result = local_result;
                        }
                    }
                }
            }
        }
#else
        // Fallback to sequential processing when OpenMP is not available
        for (IndexT k = k2s; k <= k2e; k += 2) {
            if (reverse_step<IndexT, T>(k, (int32_t)d, (int32_t)v_off, (int32_t)N, (int32_t)M, a, b, Vr, Vf, (int32_t)delta, odd_delta, result))
                return result;
        }
#endif

        if (found_solution) {
            return result;
        }    }
    
    
    return {-1,-1};     // unreachable when D ≤ N+M
}


template<typename T>
std::pair<int32_t,int32_t> Diff<T>::middle_snake_slice(
        const Slice<T>& A, const Slice<T>& B) {

    const int32_t N = A.size(), M = B.size();
    const int32_t max_d = (N + M + 1) >> 1;    
    const T* a = A.data;
    const T* b = B.data;

    // ---- adaptive index choice
    const bool use16 = max_d < 0x3FFF;          // 16‑bit safe margin
    const int32_t v_len = 2 * max_d + 3;

    if (use16) {
        Vec<int16_t> V(2*v_len, -1);
        return middle_snake_core(V.data(), V.data() + v_len,
                               v_len, N, M, a, b);
    } else {
        Vec<int32_t> V(2*v_len, -1);
        return middle_snake_core(V.data(), V.data() + v_len,
                               v_len, N, M, a, b);
    }
}

// Updated range-based versions as thin wrappers


// Explicit template instantiation for int type
template class Diff<int>;

// Slimmer C API wrapper
extern "C" {

int myers_diff_bisect(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int* x_out, int* y_out) 
{
    if (!text1 || !text2 || !x_out || !y_out) {
        return -1;
    }
    
    try {
        // Use Slice to avoid copying - zero-copy operation
        Slice<int32_t> slice1(text1, text1_length);
        Slice<int32_t> slice2(text2, text2_length);
        
        // Use the new Diff class middle_snake helper with slices
        Diff<int32_t> diff;
        auto result = diff.middle_snake_slice(slice1, slice2);
        *x_out = result.first;
        *y_out = result.second;
        return 0;
        
    } catch (const std::exception& e) {
        return -1;
    }
}
}


 // extern "C"
