#!/usr/bin/env python3
"""
Example showing different ways to use .compile() with Numba functions.

This demonstrates:
1. Basic .compile() usage
2. Compiling with specific signatures
3. Compiling multiple signatures
4. Error handling during compilation
"""

import time
import numpy as np
from numba import njit, types

# Example Numba function
@njit
def simple_diff_function(arr1, arr2):
    """Simple function to demonstrate compilation."""
    matches = 0
    min_len = min(len(arr1), len(arr2))
    for i in range(min_len):
        if arr1[i] == arr2[i]:
            matches += 1
    return matches

def demonstrate_basic_compile():
    """Demonstrate basic .compile() usage."""
    print("=== Basic .compile() Usage ===")
    
    # Method 1: Compile with dummy call (automatic signature inference)
    print("1. Compiling with dummy call...")
    start = time.time()
    dummy1 = np.array([1, 2, 3], dtype=np.int32)
    dummy2 = np.array([1, 3, 4], dtype=np.int32)
    _ = simple_diff_function(dummy1, dummy2)  # This triggers compilation
    compile_time = time.time() - start
    print(f"   Compilation time: {compile_time:.4f} seconds")
    
    # Method 2: Explicit compilation with signature
    print("2. Compiling with explicit signature...")
    
    # Create a new function to demonstrate explicit compilation
    @njit
    def explicit_diff_function(arr1, arr2):
        matches = 0
        min_len = min(len(arr1), len(arr2))
        for i in range(min_len):
            if arr1[i] == arr2[i]:
                matches += 1
        return matches
    
    start = time.time()
    int32_array = types.int32[:]
    explicit_diff_function.compile((int32_array, int32_array))
    compile_time = time.time() - start
    print(f"   Explicit compilation time: {compile_time:.4f} seconds")

def demonstrate_multiple_signatures():
    """Demonstrate compiling for multiple signatures."""
    print("\n=== Multiple Signature Compilation ===")
    
    @njit
    def multi_type_function(arr1, arr2):
        return len(arr1) + len(arr2)
    
    # Define signatures for different data types
    signatures = [
        (types.int32[:], types.int32[:]),    # int32 arrays
        (types.int64[:], types.int64[:]),    # int64 arrays
        (types.float32[:], types.float32[:]), # float32 arrays
        (types.float64[:], types.float64[:]), # float64 arrays
    ]
    
    print("Compiling for multiple signatures:")
    total_compile_time = 0
    
    for i, sig in enumerate(signatures, 1):
        try:
            start = time.time()
            multi_type_function.compile(sig)
            compile_time = time.time() - start
            total_compile_time += compile_time
            print(f"   {i}. {sig[0]} -> {compile_time:.4f}s ✅")
        except Exception as e:
            print(f"   {i}. {sig[0]} -> Failed: {e} ❌")
    
    print(f"Total compilation time: {total_compile_time:.4f} seconds")

def demonstrate_performance_comparison():
    """Compare performance of compiled vs non-compiled functions."""
    print("\n=== Performance Comparison ===")
    
    # Function without pre-compilation
    @njit
    def uncompiled_function(arr1, arr2):
        total = 0
        for i in range(len(arr1)):
            for j in range(len(arr2)):
                if arr1[i] == arr2[j]:
                    total += 1
        return total
    
    # Function with pre-compilation
    @njit
    def precompiled_function(arr1, arr2):
        total = 0
        for i in range(len(arr1)):
            for j in range(len(arr2)):
                if arr1[i] == arr2[j]:
                    total += 1
        return total
    
    # Pre-compile the second function
    int32_array = types.int32[:]
    precompiled_function.compile((int32_array, int32_array))
    
    # Test data
    test_arr1 = np.random.randint(0, 100, 1000, dtype=np.int32)
    test_arr2 = np.random.randint(0, 100, 1000, dtype=np.int32)
    
    # Test uncompiled (first call includes compilation)
    print("Testing uncompiled function (first call):")
    start = time.time()
    result1 = uncompiled_function(test_arr1, test_arr2)
    time1 = time.time() - start
    print(f"   Time: {time1:.6f} seconds, Result: {result1}")
    
    # Test precompiled
    print("Testing pre-compiled function:")
    start = time.time()
    result2 = precompiled_function(test_arr1, test_arr2)
    time2 = time.time() - start
    print(f"   Time: {time2:.6f} seconds, Result: {result2}")
    
    if time1 > time2:
        speedup = time1 / time2
        print(f"   Speedup: {speedup:.2f}x faster with pre-compilation! ✅")
    else:
        print("   Pre-compilation didn't show significant improvement ⚠️")

def demonstrate_error_handling():
    """Demonstrate proper error handling during compilation."""
    print("\n=== Error Handling ===")
    
    @njit
    def problematic_function(arr):
        # This might have issues with certain types
        return arr.sum()
    
    # Try to compile with various signatures
    test_signatures = [
        types.int32[:],
        types.float64[:],
        types.unicode_type[:],  # This might fail
    ]
    
    for sig in test_signatures:
        try:
            problematic_function.compile((sig,))
            print(f"   ✅ Successfully compiled for {sig}")
        except Exception as e:
            print(f"   ❌ Failed to compile for {sig}: {e}")

def main():
    """Run all demonstrations."""
    print("Numba .compile() Method Demonstration")
    print("=" * 50)
    
    demonstrate_basic_compile()
    demonstrate_multiple_signatures()
    demonstrate_performance_comparison()
    demonstrate_error_handling()
    
    print("\n=== Key Takeaways ===")
    print("1. Use .compile() to avoid JIT overhead on first call")
    print("2. Specify exact signatures for best performance")
    print("3. Compile for all data types your application uses")
    print("4. Handle compilation errors gracefully")
    print("5. Pre-compilation is especially beneficial for frequently called functions")

if __name__ == "__main__":
    main()
