#!/bin/bash

# Compilation script for OpenMP integration test

echo "Compiling OpenMP integration test..."

if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    # Linux or macOS
    echo "Compiling for Linux/macOS..."
    
    # Compile the test with OpenMP support
    g++ -O3 -march=native -std=c++17 -fopenmp -o test_openmp_integration test_openmp_integration.cpp myers_diff.cpp
    
    if [ $? -eq 0 ]; then
        echo "Successfully compiled test_openmp_integration"
        echo "Run with: ./test_openmp_integration"
    else
        echo "Compilation failed"
        exit 1
    fi
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # Windows
    echo "Compiling for Windows..."
    echo "Please use Visual Studio x64 Native Tools Command Prompt and run:"
    echo "cl /O2 /std:c++17 /openmp test_openmp_integration.cpp myers_diff.cpp /Fe:test_openmp_integration.exe"
else
    echo "Unknown OS type: $OSTYPE"
    exit 1
fi

echo "Compilation complete!"
