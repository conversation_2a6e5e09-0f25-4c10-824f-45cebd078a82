"""
HTML diff visualization module.

This module provides functions to generate HTML visualizations of diff results
with color-coded changes and compact display for large files.
"""

import os
import html
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime


class HtmlDiffVisualizer:
    """HTML diff visualization generator with support for large files."""
    
    def __init__(self):
        """
        Initialize the HTML diff visualizer.
        """
        self.compact_equal_threshold = 5  # Always fold equal blocks > 5 lines
        self.aggressive_optimization = True  # Enable aggressive size optimization
        
    def generate_html_diff(
        self,
        file1_path: str,
        file2_path: str,
        diff_blocks: List[List[Tuple[str, Optional[int]]]],
        file1_lines: List[str],
        file2_lines: List[str],
        output_path: str = "diff_visualization.html"
    ) -> str:
        """
        Generate HTML diff visualization.
        
        Args:
            file1_path: Path to first file
            file2_path: Path to second file  
            diff_blocks: Diff blocks from format_diff_ops_to_blocks
            file1_lines: Lines from first file
            file2_lines: Lines from second file
            output_path: Output HTML file path
            
        Returns:
            Path to generated HTML file
        """
        # Always use compact mode for equal sections > 5 lines (regardless of file size)
        file1_size_mb = self._get_file_size_mb(file1_lines)
        file2_size_mb = self._get_file_size_mb(file2_lines)
        use_compact = True  # Always enable folding for equal sections > 5 lines
        
        # Generate HTML content
        html_content = self._generate_html_content(
            file1_path, file2_path, diff_blocks, file1_lines, file2_lines, use_compact
        )
        
        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return os.path.abspath(output_path)
    
    def _get_file_size_mb(self, lines: List[str]) -> float:
        """Calculate file size in MB from lines."""
        total_chars = sum(len(line) for line in lines)
        return total_chars / (1024 * 1024)
    
    def _generate_html_content(
        self,
        file1_path: str,
        file2_path: str,
        diff_blocks: List[List[Tuple[str, Optional[int]]]],
        file1_lines: List[str],
        file2_lines: List[str],
        use_compact: bool
    ) -> str:
        """Generate complete HTML content."""
        
        # Calculate statistics
        stats = self._calculate_diff_stats(diff_blocks, file1_lines, file2_lines)
        
        html_parts = [
            self._generate_html_header(file1_path, file2_path, stats, use_compact),
            self._embed_original_files(file1_lines, file2_lines),
            self._generate_diff_content(diff_blocks, file1_lines, file2_lines, use_compact),
            self._generate_html_footer()
        ]
        
        return '\n'.join(html_parts)
    
    def _embed_original_files(self, file1_lines: List[str], file2_lines: List[str]) -> str:
        """Embed original file contents efficiently for on-demand loading."""
        import json
        
        # For maximum compatibility and simplicity, use JSON with some optimizations
        # Remove redundant whitespace and use efficient encoding
        file1_optimized = [line.rstrip() for line in file1_lines]  # Remove trailing whitespace
        file2_optimized = [line.rstrip() for line in file2_lines]
        
        # Use compact JSON encoding
        file1_js = json.dumps(file1_optimized, separators=(',', ':'))
        file2_js = json.dumps(file2_optimized, separators=(',', ':'))
        
        return f'''
        <script>
        // Original file contents for on-demand loading (optimized encoding)
        window.originalFiles = {{
            file1: {file1_js},
            file2: {file2_js}
        }};
        </script>'''

    def _generate_html_header(
        self,
        file1_path: str,
        file2_path: str,
        stats: Dict[str, Any],
        use_compact: bool
    ) -> str:
        """Generate HTML header with CSS and metadata."""
        
        compact_notice = """
        <div class="compact-notice">
            <strong>Optimized View:</strong> Equal sections with more than 5 lines are folded and loaded on-demand for better performance.
            Click ranges to load and view content. Original files are embedded for instant access.
        </div>
        """
        
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diff Visualization: {os.path.basename(file1_path)} vs {os.path.basename(file2_path)}</title>
    <style>
        body {{
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.4;
        }}
        
        .header {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        
        .file-info {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }}
        
        .file-path {{
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}
        
        .stat-item {{
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }}
        
        .stat-value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #1a73e8;
        }}
        
        .compact-notice {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
        }}
        
        .diff-container {{
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .diff-line {{
            display: grid;
            grid-template-columns: 60px 60px 1fr;
            border-bottom: 1px solid #e8eaed;
            min-height: 20px;
        }}
        
        .line-num {{
            padding: 2px 8px;
            background: #f8f9fa;
            border-right: 1px solid #e8eaed;
            text-align: right;
            color: #5f6368;
            font-size: 0.9em;
            user-select: none;
        }}
        
        .line-content {{
            padding: 2px 8px;
            white-space: pre-wrap;
            word-break: break-all;
        }}
        
        .equal {{
            background-color: white;
        }}
        
        .deletion {{
            background-color: #ffeef0;
            border-left: 4px solid #d73a49;
        }}
        
        .insertion {{
            background-color: #e6ffed;
            border-left: 4px solid #28a745;
        }}
        
        .compact-range {{
            background: #f8f9fa;
            border: 1px dashed #dadce0;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-style: italic;
            color: #5f6368;
            text-align: center;
        }}
        
        .compact-range:hover {{
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
        }}
        
        .compact-range.expanded {{
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
        }}
        
        .compact-range.loading {{
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
            cursor: wait;
        }}
        
        .compact-range.ultra-compact {{
            background: #f1f3f4;
            border: 1px solid #dadce0;
            padding: 4px 8px;
            margin: 1px 0;
            font-size: 0.85em;
            color: #5f6368;
        }}
        
        .equal-section-container {{
            border: 1px solid #e8eaed;
            border-radius: 4px;
            margin: 2px 0;
        }}
        
        .hidden {{
            display: none;
        }}
        
        .legend {{
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }}
        
        .legend-deletion {{ background-color: #ffeef0; border-left: 4px solid #d73a49; }}
        .legend-insertion {{ background-color: #e6ffed; border-left: 4px solid #28a745; }}
        .legend-equal {{ background-color: white; border: 1px solid #e8eaed; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Diff Visualization</h1>
        <div class="file-info">
            <div>
                <strong>File A:</strong>
                <div class="file-path">{html.escape(file1_path)}</div>
            </div>
            <div>
                <strong>File B:</strong>
                <div class="file-path">{html.escape(file2_path)}</div>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">{stats['total_lines_a']}</div>
                <div>Lines in A</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats['total_lines_b']}</div>
                <div>Lines in B</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats['deletions']}</div>
                <div>Deletions</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats['insertions']}</div>
                <div>Insertions</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats['diff_blocks']}</div>
                <div>Diff Blocks</div>
            </div>
        </div>
        
        {compact_notice}
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color legend-deletion"></div>
                <span>Deletions</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-insertion"></div>
                <span>Insertions</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-equal"></div>
                <span>Equal</span>
            </div>
        </div>
        
        <p><em>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
    </div>
"""
    
    def _calculate_diff_stats(
        self,
        diff_blocks: List[List[Tuple[str, Optional[int]]]],
        file1_lines: List[str],
        file2_lines: List[str]
    ) -> Dict[str, Any]:
        """Calculate diff statistics."""
        deletions = 0
        insertions = 0
        
        for block in diff_blocks:
            for op, _ in block:
                if op == '-':
                    deletions += 1
                elif op == '+':
                    insertions += 1
        
        return {
            'total_lines_a': len(file1_lines),
            'total_lines_b': len(file2_lines),
            'deletions': deletions,
            'insertions': insertions,
            'diff_blocks': len(diff_blocks)
        }
    
    def _generate_diff_content(
        self,
        diff_blocks: List[List[Tuple[str, Optional[int]]]],
        file1_lines: List[str],
        file2_lines: List[str],
        use_compact: bool
    ) -> str:
        """Generate the main diff content."""
        content_parts = ['<div class="diff-container">']
        
        current_line_a = 1
        current_line_b = 1
        
        for block_idx, block in enumerate(diff_blocks):
            if not block:
                continue
                
            # Process the block and determine equal ranges before/after
            block_content, next_line_a, next_line_b = self._process_diff_block(
                block, current_line_a, current_line_b, file1_lines, file2_lines, 
                use_compact, block_idx
            )
            
            content_parts.append(block_content)
            current_line_a = next_line_a
            current_line_b = next_line_b
        
        # Add any remaining equal lines at the end
        if current_line_a <= len(file1_lines):
            equal_content = self._generate_equal_section(
                current_line_a, len(file1_lines), current_line_b, len(file2_lines),
                file1_lines, use_compact, f"final_equal"
            )
            content_parts.append(equal_content)
        
        content_parts.append('</div>')
        
        # Always add JavaScript for folding functionality
        content_parts.append(self._generate_javascript())
        
        return '\n'.join(content_parts)
    
    def _process_diff_block(
        self,
        block: List[Tuple[str, Optional[int]]],
        start_line_a: int,
        start_line_b: int,
        file1_lines: List[str],
        file2_lines: List[str],
        use_compact: bool,
        block_idx: int
    ) -> Tuple[str, int, int]:
        """Process a single diff block."""
        content_parts = []
        
        # Find the range of lines this block affects
        del_lines = [line_num for op, line_num in block if op == '-' and line_num is not None]
        add_lines = [line_num for op, line_num in block if op == '+' and line_num is not None]
        ins_marker = next((line_num for op, line_num in block if op == 'INS'), None)
        del_marker = next((line_num for op, line_num in block if op == 'DEL'), None)
        
        # Determine the actual start position for changes
        if ins_marker is not None:
            change_start_a = ins_marker + 1
            change_start_b = start_line_b
        elif del_lines:
            change_start_a = min(del_lines)
            change_start_b = start_line_b
        else:
            change_start_a = start_line_a
            change_start_b = start_line_b
        
        # Add equal lines before the change
        if start_line_a < change_start_a:
            equal_end_a = change_start_a - 1
            equal_end_b = start_line_b + (equal_end_a - start_line_a)
            equal_content = self._generate_equal_section(
                start_line_a, equal_end_a, start_line_b, equal_end_b,
                file1_lines, use_compact, f"equal_before_{block_idx}"
            )
            content_parts.append(equal_content)
            start_line_b = equal_end_b + 1
        
        # Add the actual changes
        for op, line_num in block:
            if op == '-' and line_num is not None:
                line_content = file1_lines[line_num - 1] if line_num <= len(file1_lines) else ""
                content_parts.append(self._generate_diff_line(
                    line_num, None, html.escape(line_content), "deletion"
                ))
            elif op == '+' and line_num is not None:
                line_content = file2_lines[line_num - 1] if line_num <= len(file2_lines) else ""
                content_parts.append(self._generate_diff_line(
                    None, line_num, html.escape(line_content), "insertion"
                ))
        
        # Calculate next line positions
        next_line_a = max(del_lines) + 1 if del_lines else change_start_a
        next_line_b = max(add_lines) + 1 if add_lines else start_line_b
        
        return '\n'.join(content_parts), next_line_a, next_line_b
    
    def _generate_equal_section(
        self,
        start_a: int,
        end_a: int,
        start_b: int,
        end_b: int,
        file1_lines: List[str],
        use_compact: bool,
        section_id: str
    ) -> str:
        """Generate equal section with aggressive optimization for large sections."""
        if start_a > end_a:
            return ""
            
        line_count = end_a - start_a + 1
        
        # For aggressive optimization, fold even smaller sections and show minimal context
        if self.aggressive_optimization and line_count > 3:
            return self._generate_ultra_compact_equal_section(
                start_a, end_a, start_b, end_b, line_count, section_id, file1_lines
            )
        elif line_count > self.compact_equal_threshold:
            return self._generate_compact_equal_section(
                start_a, end_a, start_b, end_b, line_count, section_id, file1_lines
            )
        else:
            return self._generate_full_equal_section(
                start_a, end_a, start_b, file1_lines
            )
    
    def _generate_ultra_compact_equal_section(
        self,
        start_a: int,
        end_a: int,
        start_b: int,
        end_b: int,
        line_count: int,
        section_id: str,
        file1_lines: List[str]
    ) -> str:
        """Generate ultra-compact representation with no context lines for maximum space savings."""
        # For ultra-compact mode, show NO context lines, just the placeholder
        compact_html = f'''
        <div class="compact-range ultra-compact" onclick="loadEqualSection('{section_id}', {start_a}, {end_a}, {start_b})" id="compact_{section_id}">
            ⋯ {line_count} equal lines ({start_a}-{end_a}) ⋯ (click to load)
        </div>
        <div id="full_{section_id}" class="hidden equal-section-container">
            <!-- Content will be loaded dynamically -->
        </div>'''
        
        return compact_html

    def _generate_compact_equal_section(
        self,
        start_a: int,
        end_a: int,
        start_b: int,
        end_b: int,
        line_count: int,
        section_id: str,
        file1_lines: List[str]
    ) -> str:
        """Generate folded representation of equal section with on-demand loading."""
        # Show first few and last few lines, with folded range in between
        show_lines = 2  # Show 2 lines at start and end for better context
        
        parts = []
        
        # Show first few lines
        for i in range(min(show_lines, line_count)):
            line_a = start_a + i
            line_b = start_b + i
            if line_a <= len(file1_lines):
                line_content = html.escape(file1_lines[line_a - 1])
                parts.append(self._generate_diff_line(line_a, line_b, line_content, "equal"))
        
        # Add compact range if there are hidden lines
        if line_count > 2 * show_lines:
            hidden_start = start_a + show_lines
            hidden_end = end_a - show_lines
            hidden_count = hidden_end - hidden_start + 1
            
            # Lightweight placeholder that loads content on-demand
            compact_html = f'''
            <div class="compact-range" onclick="loadEqualSection('{section_id}', {hidden_start}, {hidden_end}, {start_b + show_lines})" id="compact_{section_id}">
                ⋯ {hidden_count} equal lines ({hidden_start}-{hidden_end}) ⋯ (click to load)
            </div>
            <div id="full_{section_id}" class="hidden equal-section-container">
                <!-- Content will be loaded dynamically -->
            </div>'''
            
            parts.append(compact_html)
            
            # Show last few lines
            for i in range(max(0, line_count - show_lines), line_count):
                line_a = start_a + i
                line_b = start_b + i
                if line_a <= len(file1_lines):
                    line_content = html.escape(file1_lines[line_a - 1])
                    parts.append(self._generate_diff_line(line_a, line_b, line_content, "equal"))
        
        return '\n'.join(parts)
    
    def _generate_full_equal_section(
        self,
        start_a: int,
        end_a: int,
        start_b: int,
        file1_lines: List[str]
    ) -> str:
        """Generate full equal section without compacting."""
        parts = []
        for i in range(start_a, end_a + 1):
            line_b = start_b + (i - start_a)
            if i <= len(file1_lines):
                line_content = html.escape(file1_lines[i - 1])
                parts.append(self._generate_diff_line(i, line_b, line_content, "equal"))
        return '\n'.join(parts)
    
    def _generate_diff_line(
        self,
        line_a: Optional[int],
        line_b: Optional[int],
        content: str,
        line_type: str
    ) -> str:
        """Generate a single diff line."""
        line_a_str = str(line_a) if line_a is not None else ""
        line_b_str = str(line_b) if line_b is not None else ""
        
        return f'''
        <div class="diff-line {line_type}">
            <div class="line-num">{line_a_str}</div>
            <div class="line-num">{line_b_str}</div>
            <div class="line-content">{content}</div>
        </div>'''
    
    def _generate_javascript(self) -> str:
        """Generate JavaScript for on-demand loading functionality."""
        return '''
        <script>
        // Cache for loaded sections to avoid re-loading
        const loadedSections = new Set();
        
        function loadEqualSection(sectionId, startLineA, endLineA, startLineB) {
            const compactElement = document.getElementById('compact_' + sectionId);
            const fullElement = document.getElementById('full_' + sectionId);
            
            if (fullElement.classList.contains('hidden')) {
                // Load content if not already loaded
                if (!loadedSections.has(sectionId)) {
                    // Show loading state
                    compactElement.classList.add('loading');
                    const originalText = compactElement.innerHTML;
                    compactElement.innerHTML = originalText.replace('click to load', 'loading...');
                    
                    // Use setTimeout to allow UI update before processing
                    setTimeout(() => {
                        loadSectionContent(sectionId, startLineA, endLineA, startLineB);
                        loadedSections.add(sectionId);
                        
                        // Remove loading state and show content
                        compactElement.classList.remove('loading');
                        fullElement.classList.remove('hidden');
                        compactElement.classList.add('expanded');
                        compactElement.innerHTML = originalText.replace('click to load', 'click to fold');
                    }, 10);
                } else {
                    // Content already loaded, just show it
                    fullElement.classList.remove('hidden');
                    compactElement.classList.add('expanded');
                    compactElement.innerHTML = compactElement.innerHTML.replace('click to load', 'click to fold');
                }
            } else {
                // Hide the section
                fullElement.classList.add('hidden');
                compactElement.classList.remove('expanded');
                compactElement.innerHTML = compactElement.innerHTML.replace('click to fold', 'click to load');
            }
        }
        
        function loadSectionContent(sectionId, startLineA, endLineA, startLineB) {
            const fullElement = document.getElementById('full_' + sectionId);
            const file1Lines = window.originalFiles.file1;
            
            let html = '';
            for (let i = startLineA; i <= endLineA; i++) {
                const lineA = i;
                const lineB = startLineB + (i - startLineA);
                const content = file1Lines[i - 1] || '';
                
                // Escape HTML content
                const escapedContent = content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;');
                
                html += `
                <div class="diff-line equal">
                    <div class="line-num">${lineA}</div>
                    <div class="line-num">${lineB}</div>
                    <div class="line-content">${escapedContent}</div>
                </div>`;
            }
            
            fullElement.innerHTML = html;
        }
        
        // Legacy function for backward compatibility
        function toggleSection(sectionId) {
            loadEqualSection(sectionId, 0, 0, 0);
        }
        </script>'''
    
    def _generate_html_footer(self) -> str:
        """Generate HTML footer."""
        return '''
</body>
</html>'''


def generate_html_diff_visualization(
    file1_path: str,
    file2_path: str,
    diff_blocks: List[List[Tuple[str, Optional[int]]]],
    file1_lines: List[str],
    file2_lines: List[str],
    output_path: str = "diff_visualization.html"
) -> str:
    """
    Convenience function to generate HTML diff visualization.
    
    Args:
        file1_path: Path to first file
        file2_path: Path to second file
        diff_blocks: Diff blocks from format_diff_ops_to_blocks
        file1_lines: Lines from first file
        file2_lines: Lines from second file
        output_path: Output HTML file path
        
    Returns:
        Path to generated HTML file
    """
    visualizer = HtmlDiffVisualizer()
    return visualizer.generate_html_diff(
        file1_path, file2_path, diff_blocks, file1_lines, file2_lines, output_path
    ) 