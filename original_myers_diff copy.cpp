#include "myers_diff.hpp"
#include <algorithm>
#include <vector>
#include <cstring>
#include <iostream> 
// Template implementation for Diff class

template<typename T>
int32_t Diff<T>::common_prefix_len(const Slice<T>& A, const Slice<T>& B) {
    int32_t n = std::min(A.size(), B.size());
    int32_t i = 0;
    while (i < n && A[i] == B[i]) ++i;
    return i;
}

template<typename T>
int32_t Diff<T>::common_suffix_len(const Slice<T>& A, const Slice<T>& B) {
    int32_t n = std::min(A.size(), B.size());
    int32_t i = 0;
    while (i < n && A[A.size() - 1 - i] == B[B.size() - 1 - i]) ++i;
    return i;
}

template<typename T>
DiffResult<T> Diff<T>::diff_main(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_main_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_main_impl(Slice<T> A, Slice<T> B) {
    // Check for equality (speedup)
    if (A.size() == B.size() &&
        std::equal(A.buf->begin() + A.begin,
                   A.buf->begin() + A.end,
                   B.buf->begin() + B.begin)) {
        if (!A.empty()) {
            return {{DIFF_EQUAL, {A.buf->begin() + A.begin,
                                  A.buf->begin() + A.end}}};
        }
        return {};
    }    // Trim off common prefix (speedup)
    int32_t prefix = common_prefix_len(A, B);
    std::vector<T> commonprefix;
    if (prefix > 0) {
        commonprefix.assign(A.buf->begin() + A.begin,
                           A.buf->begin() + A.begin + prefix);
    }
    
    Slice<T> A_trimmed{A.buf, A.begin + prefix, A.end};
    Slice<T> B_trimmed{B.buf, B.begin + prefix, B.end};

    // Trim off common suffix (speedup)
    int32_t suffix = common_suffix_len(A_trimmed, B_trimmed);
    std::vector<T> commonsuffix;
    if (suffix > 0) {
        commonsuffix.assign(A_trimmed.buf->begin() + A_trimmed.end - suffix,
                           A_trimmed.buf->begin() + A_trimmed.end);
    }
    
    Slice<T> A_mid{A_trimmed.buf, A_trimmed.begin, A_trimmed.end - suffix};
    Slice<T> B_mid{B_trimmed.buf, B_trimmed.begin, B_trimmed.end - suffix};

    // Compute the diff on the middle block
    DiffResult<T> diffs = diff_compute_impl(A_mid, B_mid);

    // Restore the prefix and suffix
    if (!commonprefix.empty()) {
        diffs.insert(diffs.begin(), {DIFF_EQUAL, commonprefix});
    }
    if (!commonsuffix.empty()) {
        diffs.push_back({DIFF_EQUAL, commonsuffix});
    }

    return diffs;
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_compute_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_compute_impl(Slice<T> A, Slice<T> B) {
    if (A.empty()) {
        return {{DIFF_INSERT, {B.buf->begin() + B.begin, B.buf->begin() + B.end}}};
    }
    if (B.empty()) {
        return {{DIFF_DELETE, {A.buf->begin() + A.begin, A.buf->begin() + A.end}}};
    }

    // Use bisect algorithm
    return diff_bisect_impl(A, B);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect(const std::vector<T>& text1, const std::vector<T>& text2) {
    return diff_bisect_impl(make_slice(text1), make_slice(text2));
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisect_impl(Slice<T> A, Slice<T> B) {
    // Cache the slice lengths
    int32_t text1_length = A.size();
    int32_t text2_length = B.size();
    int32_t max_d = (text1_length + text2_length + 1) / 2;
    int32_t v_offset = max_d;
    int32_t v_length = 2 * max_d + 1 + 2; // +2 for guard cells
    
    // Single generation-aware V buffer (half the RAM)
    VecGen V_;
    V_.ensure(2 * v_length); // Allocate for both forward and reverse
    
    // Create aliasing views - forward and reverse use separate halves
    auto* Vf = V_.data.data();           // first half for forward
    auto* Vr = V_.data.data() + v_length; // second half for reverse
    
    // Initialize using direct access after ensuring buffer size
    Vf[v_offset + 1] = 0;                    // Forward initialization
    Vr[v_offset + 1] = 0;                    // Reverse initialization
    
    int32_t delta = text1_length - text2_length;
    bool front = (delta % 2 != 0);
    
    int32_t k1start = 0, k1end = 0, k2start = 0, k2end = 0;
    
    for (int32_t d = 0; d < max_d; ++d) {
        if ((d & 1) && delta == 0) continue;   
        // Walk the front path one step
        for (int32_t k1 = -d + k1start; k1 < d + 1 - k1end; k1 += 2) {
            int32_t k1_offset = v_offset + k1;
            int32_t x1;
            // Choose predecessor - use direct array access for performance
            if (k1 == -d || (k1 != d && Vf[k1_offset - 1] < Vf[k1_offset + 1])) {
                x1 = Vf[k1_offset + 1];
            } else {
                x1 = Vf[k1_offset - 1] + 1;
            }            int32_t y1 = x1 - k1;
            
            // Advance along the diagonal
            while (x1 < text1_length && y1 < text2_length && A[x1] == B[y1]) {
                ++x1; ++y1;
            }
            
            Vf[k1_offset] = x1;
            
            if (x1 > text1_length) {
                k1end += 2;
            } else if (y1 > text2_length) {
                k1start += 2;
            } else if (front) {
                int32_t k2_offset = v_offset + delta - k1;                
                if (k2_offset >= 0 && k2_offset < v_length && Vr[k2_offset] != -1) {
                    int32_t x2 = text1_length - Vr[k2_offset];
                    if (x1 >= x2) {
                        return diff_bisectSplit_impl(A, B, x1, y1);
                    }
                }
            }
        }        
          // Walk the reverse path one step
        for (int32_t k2 = -d + k2start; k2 < d + 1 - k2end; k2 += 2) {
            int32_t k2_offset = v_offset + k2;
            int32_t x2;
            if (k2 == -d || (k2 != d && Vr[k2_offset - 1] < Vr[k2_offset + 1])) {
                x2 = Vr[k2_offset + 1];
            } else {
                x2 = Vr[k2_offset - 1] + 1;
            }
            int32_t y2 = x2 - k2;
            
            // Pointer-based snake extension for reverse path (auto-vectorization)
            const T* a = A.buf->data() + A.begin + text1_length - 1;
            const T* b = B.buf->data() + B.begin + text2_length - 1;
            while (x2 < text1_length && y2 < text2_length && *(a - x2) == *(b - y2)) {
                ++x2; ++y2;
            }
            
            Vr[k2_offset] = x2;
            
            if (x2 > text1_length) {
                k2end += 2;
            } else if (y2 > text2_length) {
                k2start += 2;
            } else if (!front) {
                int32_t k1_off = v_offset + delta - k2;
                if (k1_off >= 0 && k1_off < v_length && Vf[k1_off] != -1) {
                    int32_t x1 = Vf[k1_off];                    
                    int32_t y1 = v_offset + x1 - k1_off;
                    int32_t x2_mirror = text1_length - x2;
                    if (x1 >= x2_mirror) {
                        return diff_bisectSplit_impl(A, B, x1, y1);
                    }
                }
            }
        }
    }

    // Diff took too long and hit the deadline or no commonality at all
    return {{DIFF_DELETE, {A.buf->begin() + A.begin, A.buf->begin() + A.end}}, 
            {DIFF_INSERT, {B.buf->begin() + B.begin, B.buf->begin() + B.end}}};
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisectSplit(const std::vector<T>& text1, const std::vector<T>& text2, 
                                        int32_t x, int32_t y) {
    Slice<T> A = make_slice(text1);
    Slice<T> B = make_slice(text2);
    return diff_bisectSplit_impl(A, B, x, y);
}

template<typename T>
DiffResult<T> Diff<T>::diff_bisectSplit_impl(Slice<T> A, Slice<T> B, int32_t x, int32_t y) {
    Slice<T> A_left{A.buf, A.begin, A.begin + x};
    Slice<T> B_left{B.buf, B.begin, B.begin + y};
    Slice<T> A_right{A.buf, A.begin + x, A.end};
    Slice<T> B_right{B.buf, B.begin + y, B.end};

    // Compute both diffs serially
    DiffResult<T> diffs = diff_main_impl(A_left, B_left);
    DiffResult<T> diffsb = diff_main_impl(A_right, B_right);

    // Concatenate results
    diffs.insert(diffs.end(), diffsb.begin(), diffsb.end());
    return diffs;
}
template<typename T>
std::pair<int32_t,int32_t> Diff<T>::middle_snake(
        const std::vector<T>& aVec, const std::vector<T>& bVec) {

    Slice<T> A = make_slice(aVec);
    Slice<T> B = make_slice(bVec);
    const int32_t N = A.size(), M = B.size();
    const int32_t max_d = (N + M + 1) >> 1;
    const int32_t v_off = max_d;

    VecGen V_;              // single buffer

    V_.ensure( 2 * (2*max_d + 1 + 2) );      // forward + reverse halves
    auto Vf = &V_.data[0];                   // first half
    auto Vr = &V_.data[(2*max_d + 1 + 2)];

    Vf[v_off + 1] = 0;
    Vr[v_off + 1] = 0;

    const T* a = A.buf->data() + A.begin;
    const T* b = B.buf->data() + B.begin;

    const int32_t delta = N - M;
    const bool odd_delta = delta & 1;

    int k1s=0,k1e=0,k2s=0,k2e=0;

    for (int32_t d = 0; d <= max_d; ++d) {
        
        // -------- forward sweep
        k1s = std::max(-d, k1s - 1);
        k1e = std::min( d, k1e + 1);

        for (int32_t k = k1s; k <= k1e; k += 2) {
            const int idx = v_off + k;
            int32_t x = (k == -d) ? Vf[idx+1]
                      : (k !=  d && Vf[idx-1] < Vf[idx+1])
                      ? Vf[idx+1]
                      : Vf[idx-1] + 1;
            int32_t y = x - k;

            while (x < N && y < M && a[x] == b[y]) { ++x; ++y; }
            Vf[idx] = x;

            if (odd_delta) {
                int32_t k_rev = delta - k;
                if (k_rev >= - (d-1) && k_rev <= (d-1)) {
                    const int32_t revX = N - Vr[v_off + k_rev];
                    if (x >= revX) return {x, y};      // bingo
                }
            }
        }

        // -------- reverse sweep (symm.)
        k2s = std::max(-d, k2s - 1);
        k2e = std::min( d, k2e + 1);

        for (int32_t k = k2s; k <= k2e; k += 2) {
            const int idx = v_off + k;
            int32_t x = (k == -d) ? Vr[idx+1]
                      : (k !=  d && Vr[idx-1] < Vr[idx+1])
                      ? Vr[idx+1]
                      : Vr[idx-1] + 1;
            int32_t y = x - k;

            while (x < N && y < M &&
                   a[N-1 - x] == b[M-1 - y]) { ++x; ++y; }
            Vr[idx] = x;

            if (!odd_delta) {
                int32_t k_for = delta - k;
                if (k_for >= -d && k_for <= d) {
                    const int32_t forX = Vf[v_off + k_for];
                    const int32_t revX = N - x;
                    if (forX >= revX) return {forX, forX - k_for};
                }
            }
        }
        /* no need to clear V_: generation clock hides stale data */
    }
    return {-1,-1};     // unreachable when D ≤ N+M
}
// Updated prefix/suffix detection using linear scan
template<typename T>
int32_t Diff<T>::diff_commonPrefix(const std::vector<T>& text1, const std::vector<T>& text2) {
    int32_t n = std::min(text1.size(), text2.size());
    int32_t i = 0;
    while (i < n && text1[i] == text2[i]) ++i;
    return i;
}

template<typename T>
int32_t Diff<T>::diff_commonSuffix(const std::vector<T>& text1, const std::vector<T>& text2) {
    int32_t n = std::min(text1.size(), text2.size());
    int32_t i = 0;
    while (i < n && text1[text1.size() - 1 - i] == text2[text2.size() - 1 - i]) ++i;
    return i;
}

template<typename T>
bool Diff<T>::list_endswith(const std::vector<T>& list1, const std::vector<T>& list2) {
    if (list2.empty() || list2.size() > list1.size()) {
        return false;
    }
    return std::equal(list2.begin(), list2.end(), list1.end() - list2.size());
}

template<typename T>
bool Diff<T>::list_startswith(const std::vector<T>& list1, const std::vector<T>& list2) {
    if (list2.empty() || list2.size() > list1.size()) {
        return false;
    }
    return std::equal(list2.begin(), list2.end(), list1.begin());
}

template<typename T>
int Diff<T>::find_subsequence(typename std::vector<T>::const_iterator longtext_begin,
                              typename std::vector<T>::const_iterator longtext_end,
                              typename std::vector<T>::const_iterator shorttext_begin,
                              typename std::vector<T>::const_iterator shorttext_end) {    if (shorttext_begin == shorttext_end) {
        return 0;
    }
    
    int32_t longtext_length = std::distance(longtext_begin, longtext_end);
    int32_t shorttext_length = std::distance(shorttext_begin, shorttext_end);
    
    if (longtext_length < shorttext_length) {
        return -1;
    }
      for (int32_t i = 0; i <= longtext_length - shorttext_length; ++i) {
        if (std::equal(shorttext_begin, shorttext_end, longtext_begin + i)) {
            return static_cast<int>(i);
        }
    }
    
    return -1;
}

// Updated range-based versions as thin wrappers


// Explicit template instantiation for int type
template class Diff<int>;

// Slimmer C API wrapper
extern "C" {

int myers_diff_bisect(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int* x_out, int* y_out) 
{
    if (!text1 || !text2 || !x_out || !y_out) {
        return -1;
    }
    
    try {
        // Convert C arrays to vectors
        std::vector<int> text1_vec(text1, text1 + text1_length);
        std::vector<int> text2_vec(text2, text2 + text2_length);
        // Use the new Diff class middle_snake helper
        Diff<int> diff;
        auto result = diff.middle_snake(text1_vec, text2_vec);
          *x_out = result.first;
        *y_out = result.second;
        return 0;
        
    } catch (const std::exception& e) {
        return -1;
    }
}

// New C API function for full diff computation
MYERS_API int myers_diff_compute(
    const int* text1, int text1_length,
    const int* text2, int text2_length,
    int** operations_out,   // Output: array of operation types
    int*** data_out,        // Output: array of data arrays
    int** data_lengths_out, // Output: array of data lengths
    int* result_count_out   // Output: number of diff operations
) {
    if (!text1 || !text2 || !operations_out || !data_out || 
        !data_lengths_out || !result_count_out) {
        return -1;
    }
    
    try {
        // Check for negative lengths
        if (text1_length < 0 || text2_length < 0) {
            return -1;
        }          // Check for extremely large inputs that might cause allocation failures
        const int32_t MAX_SIZE = 100000000; // 100 million elements
        if (text1_length > MAX_SIZE || text2_length > MAX_SIZE) {
            return -1;
        }
        
        // Convert C arrays to vectors
        std::vector<int> text1_vec(text1, text1 + text1_length);
        std::vector<int> text2_vec(text2, text2 + text2_length);
        
        // Use the new Diff class
        Diff<int> diff;
        DiffResult<int> result = diff.diff_main(text1_vec, text2_vec);
          // Convert result back to C arrays
        *result_count_out = static_cast<int>(result.size());
        
        // Handle empty result case
        if (*result_count_out == 0) {
            *operations_out = nullptr;
            *data_out = nullptr;
            *data_lengths_out = nullptr;
            return 0;
        }
        
        // Allocate arrays for results
        *operations_out = (int*)malloc(*result_count_out * sizeof(int));
        *data_out = (int**)malloc(*result_count_out * sizeof(int*));
        *data_lengths_out = (int*)malloc(*result_count_out * sizeof(int));
        
        if (!*operations_out || !*data_out || !*data_lengths_out) {
            // Cleanup on allocation failure
            if (*operations_out) free(*operations_out);
            if (*data_out) free(*data_out);
            if (*data_lengths_out) free(*data_lengths_out);
            *operations_out = nullptr;
            *data_out = nullptr;
            *data_lengths_out = nullptr;
            return -1;
        }
        
        for (int i = 0; i < *result_count_out; ++i) {
            (*operations_out)[i] = result[i].first;
            (*data_lengths_out)[i] = static_cast<int>(result[i].second.size());
            
            // Handle empty data case
            if ((*data_lengths_out)[i] == 0) {
                (*data_out)[i] = nullptr;
                continue;
            }
              // Allocate data array for this operation
            (*data_out)[i] = (int*)malloc((*data_lengths_out)[i] * sizeof(int));
            if (!(*data_out)[i]) {
                // Cleanup on allocation failure
                for (int j = 0; j < i; ++j) {
                    if ((*data_out)[j]) free((*data_out)[j]);
                }
                free(*operations_out);
                free(*data_out);
                free(*data_lengths_out);
                *operations_out = nullptr;
                *data_out = nullptr;
                *data_lengths_out = nullptr;
                return -1;
            }
            
            // Copy data safely
            for (int j = 0; j < (*data_lengths_out)[i]; ++j) {
                (*data_out)[i][j] = result[i].second[j];
            }
        }
        
        return 0;
        
    } catch (const std::bad_alloc& e) {
        // Memory allocation failure
        return -2;
    } catch (const std::exception& e) {
        // Other exceptions
        return -1;
    } catch (...) {
        // Unknown exception
        return -1;
    }
}

// Helper function to free memory allocated by myers_diff_compute
MYERS_API void myers_diff_free(
    int* operations,
    int** data,
    int* data_lengths,
    int result_count
) {
    if (data) {
        for (int i = 0; i < result_count; ++i) {
            if (data[i]) {
                free(data[i]);
            }
        }
        free(data);
    }
    if (operations) {
        free(operations);
    }
    if (data_lengths) {
        free(data_lengths);
    }
}

} // extern "C"
