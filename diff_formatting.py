"""
Diff output formatting utilities.

This module provides functions to format diff operations into various output formats
that can be consumed by different applications.
"""

from typing import List, Tuple, Optional


def format_diff_ops_to_blocks(
    final_diff_ops: List[Tuple[str, Optional[int], Optional[int], str]],
    file1_lines: List[str],
    file2_lines: List[str]
) -> List[List[Tuple[str, Optional[int]]]]:
    """
    Converts detailed diff operations into a block-based format with proper ordering.

    Args:
        final_diff_ops: List of tuples (op, a_line_num, b_line_num, text)
                        where op is '+', '-', or 'equal'. Line numbers are 1-based.
        file1_lines: Original lines of file 1.
        file2_lines: Original lines of file 2.

    Returns:
        A list of lists, where each inner list represents a block of changes
        with proper ordering:
        - Pure insertions: [('INS', ref_line), ('+', line1), ('+', line2), ...]
        - Pure deletions: [('-', line1), ('-', line2), ..., ('DEL', ref_line)]
        - Mixed changes: [('-', line1), ('-', line2), ..., ('+', line1), ('+', line2), ...]
    """
    diff_blocks = []
    current_deletions: List[Tuple[str, Optional[int]]] = []
    current_insertions: List[Tuple[str, Optional[int]]] = []
    last_op_was_equal = True
    
    last_a_line_num_before_change = 0
    last_b_line_num_before_change = 0

    def finalize_current_block():
        """Finalize the current block with proper ordering and markers."""
        if not current_deletions and not current_insertions:
            return
            
        block = []
        
        # Determine block type and apply proper ordering
        is_pure_del = current_deletions and not current_insertions
        is_pure_ins = current_insertions and not current_deletions
        
        if is_pure_ins:
            # Pure insertion: INS marker first, then all insertions
            block.append(("INS", last_a_line_num_before_change))
            block.extend(current_insertions)
        elif is_pure_del:
            # Pure deletion: all deletions first, then DEL marker at end
            block.extend(current_deletions)
            block.append(("DEL", last_b_line_num_before_change))
        else:
            # Mixed block: all deletions on left, all insertions on right
            block.extend(current_deletions)
            block.extend(current_insertions)
        
        diff_blocks.append(block)

    for op, a_line_num, b_line_num, _ in final_diff_ops:
        if op == "equal":
            if not last_op_was_equal:
                # End of a change block, finalize it
                finalize_current_block()
                # Reset for next block
                current_deletions = []
                current_insertions = []

            # Update reference line numbers for next potential change block
            if a_line_num is not None: 
                last_a_line_num_before_change = a_line_num
            if b_line_num is not None: 
                last_b_line_num_before_change = b_line_num
            last_op_was_equal = True

        else:
            # Inside a change block - collect deletions and insertions separately
            if op == "-":
                current_deletions.append(("-", a_line_num))
            elif op == "+":
                current_insertions.append(("+", b_line_num))
            last_op_was_equal = False

    # Handle any remaining block at the end
    if not last_op_was_equal:
        finalize_current_block()

    return diff_blocks


def apply_formatted_diff(
    source_lines: List[str], 
    diff_blocks: List[List[Tuple[str, Optional[int]]]], 
    target_lines: List[str], 
    trace: bool = False, 
    case_name: str = ""
) -> List[str]:
    """
    Applies a diff in block format to transform source_lines.

    Args:
        source_lines: List of lines from the source file.
        diff_blocks: List of diff blocks (output of format_diff_ops_to_blocks).
        target_lines: List of lines from the target file (used for additions).
        trace: Whether to print detailed tracing information.
        case_name: Name of test case (for debug output).

    Returns:
        A new list of lines representing the transformed file.
    """
    result = []
    src_idx = 0  # Current index in source_lines (0-based)
    processed_source_indices = set()  # Track which source lines we've processed
    
    if trace:
        print(f"\n=== TRACE: {case_name} ===")
        print(f"Source file length: {len(source_lines)}")
        print(f"Target file length: {len(target_lines)}")
        print(f"Number of diff blocks: {len(diff_blocks)}")

    for block_idx, block in enumerate(diff_blocks):
        if not block: 
            if trace: print(f"Block {block_idx}: Empty, skipping")
            continue # Skip empty blocks

        # Identify line numbers involved in this block
        del_lines_1based = [line_num for op, line_num in block if op == '-' and line_num is not None]
        del_indices_0based = [i-1 for i in del_lines_1based]  # Convert to 0-based
        
        add_lines_1based = [line_num for op, line_num in block if op == '+' and line_num is not None]
        add_indices_0based = [i-1 for i in add_lines_1based]  # Convert to 0-based
        
        ins_marker_pos_0based = next((line_num for op, line_num in block if op == 'INS'), None)
        del_marker_pos_0based = next((line_num for op, line_num in block if op == 'DEL'), None)

        if trace:
            print(f"\nProcessing Block {block_idx}: {block}")
            print(f"  Del indices (0-based): {del_indices_0based}")
            print(f"  Add indices (0-based): {add_indices_0based}")
            print(f"  INS marker: {ins_marker_pos_0based}, DEL marker: {del_marker_pos_0based}")
            print(f"  src_idx before: {src_idx}")

        # Determine start position for this change block
        start_pos = None
        if ins_marker_pos_0based is not None:
            # Pure insertion - position is where to insert (no deletions)
            start_pos = ins_marker_pos_0based
            if trace: print(f"  Using INS marker as start_pos: {start_pos}")
        elif del_lines_1based:
            # Mixed change or pure deletion - position is the first deletion
            start_pos = min(del_indices_0based)
            if trace: print(f"  Using first deletion as start_pos: {start_pos}")
        
        # If we couldn't determine a start position, skip this block 
        if start_pos is None:
            if trace: print(f"  ⚠️ No start position found, skipping block")
            continue
            
        # Copy unchanged lines from source up to the start position
        lines_copied = 0
        initial_src_idx = src_idx
        while src_idx < start_pos:
            if src_idx not in processed_source_indices:
                result.append(source_lines[src_idx])
                lines_copied += 1
            src_idx += 1
            
        if trace: 
            print(f"  Copied {lines_copied} unchanged lines from source[{initial_src_idx}:{src_idx}]")
            print(f"  src_idx after copy: {src_idx}")
            
        # For pure insertions, we don't advance src_idx
        if ins_marker_pos_0based is not None and not del_lines_1based:
            if trace: print(f"  Pure insertion at {ins_marker_pos_0based}, not advancing src_idx")
            pass  # Don't advance src_idx for pure insertions
        else:
            # For deletions or mixed changes, mark deleted lines as processed
            for del_idx in del_indices_0based:
                processed_source_indices.add(del_idx)
            
            old_src_idx = src_idx
            # Advance src_idx past the deleted lines
            while src_idx <= max(del_indices_0based, default=src_idx-1):
                src_idx += 1
            
            if trace: print(f"  Skipped {src_idx - old_src_idx} deleted lines, src_idx now: {src_idx}")
                
        # Add the inserted lines from target file
        lines_added = 0
        for idx in add_indices_0based:
            if 0 <= idx < len(target_lines):
                result.append(target_lines[idx])
                lines_added += 1
            elif trace:
                print(f"  ⚠️ Warning: Addition index {idx} out of bounds for target file")
        
        if trace: print(f"  Added {lines_added} lines from target file")

    # Copy any remaining source lines that weren't deleted
    initial_src_idx = src_idx
    remaining_lines = 0
    while src_idx < len(source_lines):
        if src_idx not in processed_source_indices:
            result.append(source_lines[src_idx])
            remaining_lines += 1
        src_idx += 1
    
    if trace:
        print(f"Copied {remaining_lines} remaining lines from source[{initial_src_idx}:{len(source_lines)}]")
        print(f"Final result length: {len(result)}")
        print(f"Expected target length: {len(target_lines)}")
        print("=== END TRACE ===\n")
            
    return result 