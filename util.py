import platform


def convert_path(path: str, to_linux: bool = None) -> str:
    """
    Convert between Windows and Linux path formats.
    
    Args:
        path: The path to convert
        to_linux: If True, convert to Linux format. If False, convert to Windows format.
                 If None, auto-detect current platform and convert accordingly.
    
    Returns:
        Converted path
    """
    if not path:
        return path
    
    # Auto-detect target format if not specified
    if to_linux is None:
        to_linux = platform.system().lower() != 'windows'
    
    if to_linux:
        # Convert to Linux format
        # Handle Windows drive letters (C: -> /mnt/c)
        if path[1:3] == ':\\' or path[1:3] == ':/':
            drive = path[0].lower()
            rest = path[3:].replace('\\', '/')
            return f'/mnt/{drive}/{rest}' if rest else f'/mnt/{drive}'
        # Just convert backslashes to forward slashes
        return path.replace('\\', '/')
    else:
        # Convert to Windows format
        # Handle WSL-style paths (/mnt/c/... -> C:\...)
        if path.startswith('/mnt/') and len(path) > 5 and path[5] == '/':
            drive = path[5].upper()
            rest = path[6:].replace('/', '\\') if len(path) > 6 else ''
            return f'{drive}:{rest}'
        # Just convert forward slashes to backslashes
        return path.replace('/', '\\')


# Example usage
if __name__ == "__main__":    # Test cases
    test_paths = [
        r"C:\Users\<USER>\Documents\file.txt",
        "/home/<USER>/Documents/file.txt",
        "/mnt/c/Users/<USER>/Documents/file.txt",
        r"\\server\share\file.txt",
        "relative/path/file.txt",
        r"relative\path\file.txt"
    ]
    
    print(f"Current platform: {platform.system()}")
    print("=" * 60)
    
    for test_path in test_paths:
        to_windows = convert_path(test_path, to_linux=False)
        to_linux = convert_path(test_path, to_linux=True)
        auto_convert = convert_path(test_path)  # Auto-detect
        
        print(f"Original:     {test_path}")
        print(f"To Windows:   {to_windows}")
        print(f"To Linux:     {to_linux}")
        print(f"Auto convert: {auto_convert}")
        print("-" * 60)
