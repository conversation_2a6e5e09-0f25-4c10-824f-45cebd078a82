{"files.associations": {"algorithm": "cpp", "bit": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "cwchar": "cpp", "exception": "cpp", "vector": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iterator": "cpp", "limits": "cpp", "new": "cpp", "optional": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xtr1common": "cpp", "xutility": "cpp", "atomic": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "ctime": "cpp", "ios": "cpp", "iostream": "cpp", "istream": "cpp", "memory": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "system_error": "cpp", "typeinfo": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocinfo": "cpp", "xlocnum": "cpp", "xstring": "cpp", "charconv": "cpp", "chrono": "cpp", "forward_list": "cpp", "string": "cpp", "format": "cpp", "iomanip": "cpp", "locale": "cpp", "mutex": "cpp", "random": "cpp", "ratio": "cpp", "sstream": "cpp", "stop_token": "cpp", "thread": "cpp", "xlocbuf": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xloctime": "cpp"}}