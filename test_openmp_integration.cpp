#include "myers_diff.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <random>

#ifdef _OPENMP
#include <omp.h>
#endif

// Test function to verify OpenMP integration
void test_openmp_performance() {
    std::cout << "Testing OpenMP integration in Myers diff algorithm...\n";
    
#ifdef _OPENMP
    std::cout << "OpenMP is available. Max threads: " << omp_get_max_threads() << "\n";
#else
    std::cout << "OpenMP is NOT available. Running sequential version.\n";
#endif

    // Create test data - two large sequences with some differences
    const int size = 10000;
    std::vector<int> seq1(size);
    std::vector<int> seq2(size);
    
    // Initialize with mostly matching data
    std::random_device rd;
    std::mt19937 gen(42); // Fixed seed for reproducible results
    std::uniform_int_distribution<> dis(1, 1000);
    
    for (int i = 0; i < size; ++i) {
        seq1[i] = dis(gen);
        seq2[i] = seq1[i]; // Start with identical sequences
    }
    
    // Introduce some differences
    for (int i = 0; i < size; i += 100) {
        if (i + 50 < size) {
            seq2[i + 50] = dis(gen); // Change every 100th element
        }
    }
    
    std::cout << "Created test sequences of size " << size << " with differences every 100 elements\n";
    
    // Test the diff algorithm
    Diff<int> diff;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Create slices for zero-copy operation
    Slice<int> slice1(seq1.data(), seq1.size());
    Slice<int> slice2(seq2.data(), seq2.size());
    
    auto result = diff.middle_snake_slice(slice1, slice2);
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "Middle snake found at: (" << result.first << ", " << result.second << ")\n";
    std::cout << "Execution time: " << duration.count() << " microseconds\n";
    
    // Verify result is valid
    if (result.first >= 0 && result.second >= 0) {
        std::cout << "✓ Test passed: Valid middle snake found\n";
    } else {
        std::cout << "✗ Test failed: Invalid result\n";
    }
}

// Test with different thread counts
void test_thread_scaling() {
#ifdef _OPENMP
    std::cout << "\nTesting thread scaling performance...\n";
    
    const int size = 5000;
    std::vector<int> seq1(size);
    std::vector<int> seq2(size);
    
    // Create test data
    std::mt19937 gen(42);
    std::uniform_int_distribution<> dis(1, 1000);
    
    for (int i = 0; i < size; ++i) {
        seq1[i] = dis(gen);
        seq2[i] = (i % 50 == 0) ? dis(gen) : seq1[i]; // Differences every 50 elements
    }
    
    Diff<int> diff;
    Slice<int> slice1(seq1.data(), seq1.size());
    Slice<int> slice2(seq2.data(), seq2.size());
    
    // Test with different thread counts
    for (int threads = 1; threads <= omp_get_max_threads(); threads *= 2) {
        omp_set_num_threads(threads);
        
        auto start = std::chrono::high_resolution_clock::now();
        auto result = diff.middle_snake_slice(slice1, slice2);
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "Threads: " << threads << ", Time: " << duration.count() << " μs, Result: (" 
                  << result.first << ", " << result.second << ")\n";
    }
#else
    std::cout << "OpenMP not available - cannot test thread scaling\n";
#endif
}

int main() {
    test_openmp_performance();
    test_thread_scaling();
    
    std::cout << "\nOpenMP integration test completed.\n";
    return 0;
}
