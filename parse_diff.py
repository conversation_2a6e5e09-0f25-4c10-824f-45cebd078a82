#!/usr/bin/env python3
"""
GNU Diff Output Parser

This script parses the output of 'diff a.txt b.txt --minimal' command,
extracts diff edit blocks, counts them, and creates a trimmed output file.

Usage:
    python diff_parser.py input_file [output_file]
    
Example:
    diff a.txt b.txt --minimal > output.txt
    python diff_parser.py output.txt output_trimmed.txt
"""

import re
import sys
import os

def parse_diff_output(input_file, output_file="output_trimmed.txt"):
    """
    Parse GNU diff output and extract edit blocks.
    
    Args:
        input_file (str): Path to the diff output file
        output_file (str): Path to the trimmed output file
        
    Returns:
        tuple: (edit_blocks, count)
    """
    
    # Regular expression to match diff edit lines
    # Matches patterns like: 86391,86395c86419,86423, 0a1,10, 1000d20
    diff_pattern = re.compile(r'^\d+(?:,\d+)?[acd]\d+(?:,\d+)?$')
    
    edit_blocks = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if diff_pattern.match(line):
                    edit_blocks.append(line)
    
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return [], 0
    except Exception as e:
        print(f"Error reading file '{input_file}': {e}")
        return [], 0
    
    # Write trimmed output
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for block in edit_blocks:
                f.write(block + '\n')
        print(f"Trimmed output written to: {output_file}")
    except Exception as e:
        print(f"Error writing to '{output_file}': {e}")
    
    return edit_blocks, len(edit_blocks)

def analyze_edit_blocks(edit_blocks):
    """
    Analyze the types of edit operations.
    
    Args:
        edit_blocks (list): List of edit block strings
        
    Returns:
        dict: Statistics about edit operations
    """
    stats = {'changes': 0, 'additions': 0, 'deletions': 0}
    
    for block in edit_blocks:
        if 'c' in block:
            stats['changes'] += 1
        elif 'a' in block:
            stats['additions'] += 1
        elif 'd' in block:
            stats['deletions'] += 1
    
    return stats

def main():
    """Main function to handle command line arguments and execute parsing."""
    
    if len(sys.argv) < 2:
        print("Usage: python diff_parser.py input_file [output_file]")
        print("Example: python diff_parser.py output.txt output_trimmed.txt")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output_trimmed.txt"
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist.")
        sys.exit(1)
    
    print(f"Parsing diff output from: {input_file}")
    print("-" * 50)
    
    # Parse the diff output
    edit_blocks, block_count = parse_diff_output(input_file, output_file)
    
    if block_count == 0:
        print("No diff edit blocks found in the input file.")
        return
    
    # Print results
    print(f"Number of diff edit blocks: {block_count}")
    
    # Analyze edit operations
    stats = analyze_edit_blocks(edit_blocks)
    print(f"Changes (c): {stats['changes']}")
    print(f"Additions (a): {stats['additions']}")
    print(f"Deletions (d): {stats['deletions']}")
    
    print("\nFirst 10 edit blocks:")
    for i, block in enumerate(edit_blocks[:10]):
        print(f"  {i+1}: {block}")
    
    if len(edit_blocks) > 10:
        print(f"  ... and {len(edit_blocks) - 10} more blocks")

if __name__ == "__main__":
    main()
